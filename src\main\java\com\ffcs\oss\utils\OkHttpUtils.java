package com.ffcs.oss.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022/11/4 11:29
 */
public class OkHttpUtils {
    private static final Logger log = LoggerFactory.getLogger(OkHttpUtils.class);

    /**
     * 传给服务目录编码
     */
    public static final String SERV_CODE = "servCode";
    /**
     * 请求体内容类型value
     */
    public static final String CONTENT_TYPE_VALUE = "application/json; charset=utf-8";

    /**
     * 内容类型key
     */
    public static final String CONTENT_TYPE_KEY = "Content-Type";

    /**
     * 请求头
     */
    public static final String CONTENT_TYPE_VALUE_HEADER = "application/json";

    /**
     * json类型
     */
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    /**
     * 最大连接数
     */
    private static final int MAX_IDLE_CONNECTIONS = 5;
    /**
     * 保存连接时间（五分钟）
     */
    private static final long KEEP_ALIVE_DURATION = 5 * 60 * 1000;

    /**
     * getClient
     *
     * @return OkHttpClient
     */

    private static OkHttpClient getClient() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder().
                connectTimeout(180, TimeUnit.SECONDS).
                readTimeout(180, TimeUnit.SECONDS).
                writeTimeout(180, TimeUnit.SECONDS).
                build();
        return okHttpClient;
    }


    /**
     * getRequest
     *
     * @param url url
     * @return Request
     * @throws MalformedURLException 异常
     */
    private static Request getRequest(String url) throws MalformedURLException {
        Request.Builder builder = getBuilder();
        URL uri = new URL(url);
        return builder.url(uri).build();
    }


    /**
     * doGet
     *
     * @param url   url
     * @param param param
     * @return 结果
     */

    public static Map doGet(String url, String param) {
        if (param != null) {
            url = url + "?" + param;
        }
        OkHttpClient okHttpClient = getClient();
        Map result=new HashMap();
        String resultStr = null;
        try {
            Request request = getRequest(url);
            Response response = okHttpClient.newCall(request).execute();
            int code = response.code();
            String msg = response.message();
            if (code == HttpStatus.OK.value()) {
                ResponseBody body = response.body();
                if (body != null) {
                    resultStr = body.string();
                    result=new ObjectMapper().readValue(resultStr,Map.class);
                } else {
                    throw new IOException("response body is null");
                }
            } else {
                response.close();
                log.error("GET请求地址:" + url + "返回状态异常,code值为:" + code + "异常信息:" + msg);
            }
        } catch (IOException e) {
            log.error("GET请求异常,url = {}", url, e);
        }
        return result;
    }

    /**
     * getBuilder
     *
     * @return Builder
     */

    private static Request.Builder getBuilder() {
        Request.Builder builder = new Request.Builder();

        builder.addHeader("accept", "application/json").
                addHeader("connection", "Keep-Alive").
                addHeader(CONTENT_TYPE_KEY, CONTENT_TYPE_VALUE);
        return builder;
    }

    public static Map doPostBySpecitalParam(String url,String param){
        OkHttpClient client = new OkHttpClient();
        Map result = new HashMap();
        String resultStr;
        try {
            MediaType mediaType = MediaType.parse(CONTENT_TYPE_VALUE_HEADER);
            RequestBody reqBody = RequestBody.create(mediaType, param);
            Request request = new Request.Builder()
                    .url(url)
                    .post(reqBody)
                    .addHeader(CONTENT_TYPE_KEY, CONTENT_TYPE_VALUE_HEADER)
                    .addHeader("cache-control", "no-cache")
                    .build();
            Response response = client.newCall(request).execute();
            int code = response.code();
            String msg = response.message();
            if (code == HttpStatus.OK.value()) {
                ResponseBody body = response.body();
                if (body != null) {
                    resultStr = body.string();
                    result = new ObjectMapper().readValue(resultStr, Map.class);
                } else {
                    throw new IOException("response body is null");
                }
            } else {
                response.close();
                log.error("POST请求地址:" + url + "参数:" + param + "返回状态异常,code值为:" + code + "异常信息:" + msg);
            }
        } catch (IOException e) {
            log.error("POST请求异常,url = {}", url, e);
        }
        return result;
    }

    /**
     * doPost
     *
     * @param url   url
     * @param param param
     * @return 请求结果
     */

    public static String doPost(String url, String param) {

        OkHttpClient okHttpClient = getClient();
        Request.Builder builder = getBuilder();
        String resultStr = null;
        try {
            RequestBody requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE),
                    param.getBytes("UTF-8"));
            builder.post(requestBody);
            Request request = builder.url(url).build();
            Response response = okHttpClient.newCall(request).execute();
            int code = response.code();
            String msg = response.message();
            if (code == HttpStatus.OK.value()) {
                ResponseBody body = response.body();
                if (body != null) {
                    resultStr = body.string();
                } else {
                    throw new IOException("response body is null");
                }
            } else {
                response.close();
                log.error("POST请求地址:" + url + "参数:" + param + "返回状态异常,code值为:" + code + "异常信息:" + msg);
            }
        } catch (Exception e) {
            log.error("POST请求异常,url = {}", url, e);
        }
        return resultStr;
    }

    public static Map doPost2(String url, String param) {

        OkHttpClient okHttpClient = getClient();
        Request.Builder builder = getBuilder();
        Map result = new HashMap();
        String resultStr;
        try {
            RequestBody requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE),
                    param.getBytes("UTF-8"));
            builder.post(requestBody);
            Request request = builder.url(url).build();
            Response response = okHttpClient.newCall(request).execute();
            int code = response.code();
            String msg = response.message();
            if (code == HttpStatus.OK.value()) {
                ResponseBody body = response.body();
                if (body != null) {
                    resultStr = body.string();
                    result = new ObjectMapper().readValue(resultStr, Map.class);
                } else {
                    throw new IOException("response body is null");
                }
            } else {
                response.close();
                log.error("POST请求地址:" + url + "参数:" + param + "返回状态异常,code值为:" + code + "异常信息:" + msg);
            }
        } catch (Exception e) {
            log.error("POST请求异常,url = {}", url, e);
        }
        return result;
    }

    /**
     * doPut
     *
     * @param url   url
     * @param param param
     * @return 请求结果
     */

    public static String doPut(String url, String param) {

        OkHttpClient okHttpClient = getClient();
        Request.Builder builder = getBuilder();
        String result = null;
        try {
            RequestBody requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE),
                    param.getBytes("UTF-8"));
            builder.put(requestBody);
            Request request = builder.url(url).build();
            Response response = okHttpClient.newCall(request).execute();
            int code = response.code();
            String msg = response.message();
            if (code == HttpStatus.OK.value()) {
                ResponseBody body = response.body();
                if (body != null) {
                    result = body.string();
                } else {
                    throw new IOException("response body is null");
                }
            } else {
                response.close();
                log.error("PUT请求地址:" + url + "参数:" + param + "返回状态异常,code值为:" + code + "异常信息:" + msg);
            }
        } catch (Exception e) {
            log.error("PUT请求异常,url = {}", url, e);
        }
        return result;
    }

    private OkHttpUtils() {

    }
}
