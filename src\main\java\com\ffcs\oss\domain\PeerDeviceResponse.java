package com.ffcs.oss.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 图谱查询对端设备响应实体
 */
@Data
public class PeerDeviceResponse {
    private PeerDeviceResponseBody body;
    private PeerDeviceResponseHead head;
    private Boolean success;

    @Data
    public static class PeerDeviceResponseHead {
        private String resTime;
        private String ticket;
        private Integer respCode;
        private String respMsg;
    }

    @Data
    public static class PeerDeviceResponseBody {
        private Integer pageNo;
        private Integer pageSize;
        private Integer total;
        private List<PeerDeviceInfo> records;
    }

    @Data
    public static class PeerDeviceInfo {
        /**
         * 对端设备名称
         */
        private String deviceName;
        
        /**
         * 对端设备IP
         */
        private String deviceIp;
        
        /**
         * 设备类型
         */
        private String deviceType;
        
        /**
         * 设备ID
         */
        private String deviceId;
        
        /**
         * 其他属性根据实际图谱接口返回结构调整
         */
        private String entityName;
        private String entityType;
        private String entityId;
    }
}
