
package com.ffcs.oss.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ffcs.oss.config.CommonConstant;
import com.ffcs.oss.config.ElasticsearchConfig;
import com.ffcs.oss.config.HnConfigProperties;
import com.ffcs.oss.domain.ApiCloudQuery;
import com.ffcs.oss.domain.ApiDhQuery;
import com.ffcs.oss.domain.ApiGzQuery;
import com.ffcs.oss.domain.ServiceResp;
import com.ffcs.oss.es.utils.EsHighClientUtil;
import com.ffcs.oss.es.utils.PropertyConfig;
import com.ffcs.oss.service.IHnApiService;
import com.ffcs.oss.utils.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2021/8/26  15:06
 */
@Slf4j
public class HnApiServiceImpl_bak {

    private HnConfigProperties configProperties;

    private ElasticsearchConfig elasticsearchConfig;

    @Value("${elasticsearch.index.alarm_data_index:/alarm_data_index/_search}")
    private String alarmDataIndex;

    private RestHighLevelClient esClient;

    private void initEsCilent() throws Exception {
        // 初始化esClient
        esClient = EsHighClientUtil.getClient(JSONObject.parseObject(JSONObject.toJSONString(this.elasticsearchConfig), PropertyConfig.class));
    }

    @PostConstruct
    public void init() {
        try {
            log.info("初始化es客户端");
            // 初始化esClient
            initEsCilent();
        } catch (Exception e) {
            log.error("es init failed", e);
        }
    }

    private String buildEsDsl(String alarmStatus, String major, String equipmentName, String gwparam, String tdparam, String qyparam) {
        String dsl = queryDsl.replace("#status#", alarmStatus).replace("#equipmentName#", equipmentName).replace("#major#", major).replace("#gwparam#", gwparam).replace("#tdparam#", tdparam).replace("#qyparam#", qyparam);
        return dsl;
    }

    private String buildEsDslAlarm(String alarmStatus, String equipmentName) {
        String dsl = queryDslAlarm.replace("#status#", alarmStatus).replace("#equipmentName#", equipmentName);
        return dsl;
    }

    private String buildEsDslJz(String alarmStatus, String major, String equipmentName) {
        String dsl = queryDslJz.replace("#status#", alarmStatus).replace("#major#", major).replace("#equipmentName#", equipmentName);
        return dsl;
    }

    private String queryDsl = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"major\": \"#major#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"wildcard\": {\n" +
            "            \"equipmentName\": \"*#equipmentName#*\"\n" +
            "          }\n" +
            "        },\n" +
            "       {\n" +
            "          \"bool\": {\n" +
            "            \"should\": [\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"alarmTitle\": \"*#gwparam#*\"\n" +
            "                }\n" +
            "              },\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"alarmTitle\": \"*#tdparam#*\"\n" +
            "                }\n" +
            "              },\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"alarmTitle\": \"*#qyparam#*\"\n" +
            "                }\n" +
            "              }\n" +
            "            ],\n" +
            "            \"minimum_should_match\": 1\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";

    private String queryDslAlarm = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"equipmentName\": \"#equipmentName#\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";
    private String queryDslJz = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"major\": \"#major#\"\n" +
            "          }\n" +
            "        },\n" +
            "       {\n" +
            "          \"bool\": {\n" +
            "            \"should\": [\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"equipmentName\": \"*#equipmentName#*\"\n" +
            "                }\n" +
            "              }\n" +
            "            ],\n" +
            "            \"minimum_should_match\": 1\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";


    public ServiceResp ckTransDataFault_bak(ApiGzQuery evt) throws Exception {
        if (evt.getType() == null) {
            return ServiceResp.getInstance().error("参数错误:分类不能为空");
        }
        if (StringUtils.isBlank(evt.getNeName())) {
            return ServiceResp.getInstance().error("参数错误：设备名称不能为空");
        }
        // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
        String neCode = "";
        String apiResult;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("tgName", CommonConstant.TG_NAME);
            params.put("deviceName", evt.getNeName());
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{},入参:{}", configProperties.getQueryNenameUrl(), JSONObject.toJSONString(params));
            apiResult = OkHttpUtils.doPost(configProperties.getQueryNenameUrl(), JSON.toJSONString(params));
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{}，返回内容:{}", configProperties.getQueryNenameUrl(), apiResult);
        }
        if (apiResult == null) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
        }
        JSONObject map = JSONObject.parseObject(apiResult);
        if (map == null || !map.getBoolean("success")) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
        }
        JSONArray array = map.getJSONArray("body");
        if (array != null && !array.isEmpty()) {
            JSONObject jsonObject = array.getJSONObject(0);
            neCode = jsonObject.getString("stationCode");
        }
        if (StringUtils.isBlank(neCode)) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站，未获取到内容");
        }
        // 根据局站编码查询es库中设备名称是否有包含局站编码的告警，且告警标题包含停电、欠压或高温，并且告警状态alarmStatus为1，且专业为9
        // 构建dsl语句
        String replace = buildEsDsl(CommonConstant.ALARM_STATUS, CommonConstant.MAJOR, neCode, CommonConstant.GW, CommonConstant.TD, CommonConstant.QY);
        log.info("根据局站编码查询es库中设备名称dsl:{}", replace);
        // 调用es接口
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, replace, HttpMethod.POST.name(), esClient);
        log.info("根据局站编码查询es库中设备名称返回:{}", tempEs);
        JSONObject esJzJson = JSON.parseObject(tempEs);
        JSONObject hits = esJzJson.getJSONObject("hits");
        if (hits == null) {
            return ServiceResp.getInstance().error("根据局站编码查询es库中设备名称是否有包含局站编码的告警，未获取到内容");
        }
        Integer total = hits.getJSONObject("total").getInteger("value");
        if (total == null || total == 0) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        JSONArray esDatas = hits.getJSONArray("hits");
        if (esDatas != null && !esDatas.isEmpty()) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("result", "存在动环告警，定位动环故障:");
            String fultName = "";
            for (Object obj : esDatas) {
                JSONObject jsonObject = JSON.parseObject(obj.toString()).getJSONObject("_source");
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.TD) && !fultName.contains(CommonConstant.TD)) {
                    fultName += CommonConstant.TD + ";";
                }
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.GW) && !fultName.contains(CommonConstant.GW)) {
                    fultName += CommonConstant.GW + ";";
                }
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.QY) && !fultName.contains(CommonConstant.QY)) {
                    fultName += CommonConstant.QY + ";";
                }
            }
            if (StringUtils.isBlank(fultName)) {
                return ServiceResp.getInstance().error(2, "无故障");
            }
            resultMap.put("fultName", fultName.substring(0, fultName.length() - 1));
            return ServiceResp.getInstance().success(resultMap);
        }
        // 若查询不到： 根据设备名称（等于）查询es库中告警，告警状态alarmStatus为1；根据查询到的告警名称（完成匹配）
        // 构建dsl语句
        String replaceDsl = buildEsDslAlarm(CommonConstant.ALARM_STATUS, evt.getNeName());
        log.info("根据设备名称查询es库中告警dsl:{}", replaceDsl);
        // 调用es接口
        String tempEsAlarm = esHighClientUtil.dslHttp(alarmDataIndex, replaceDsl, HttpMethod.POST.name(), esClient);
        log.info("根据设备名称查询es库中告警返回:{}", tempEsAlarm);
        JSONObject tempJsonAlarm = JSON.parseObject(tempEsAlarm);
        JSONObject hitsAlarm = tempJsonAlarm.getJSONObject("hits");
        if (hitsAlarm == null) {
            return ServiceResp.getInstance().error("根据局站编码查询es库中设备名称是否有包含局站编码的告警，未获取到内容");
        }
        Integer totalAlarm = hitsAlarm.getJSONObject("total").getInteger("value");
        if (totalAlarm == null || totalAlarm == 0) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        JSONArray esDatasAlarm = hits.getJSONArray("hits");
        if (esDatasAlarm != null && esDatasAlarm.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        // 调用故障告警图谱接口获取到的故障根因
        String apiResultGz;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResultGz = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            if (CommonConstant.TYPE_MAJOR.equals(evt.getType())) {
                params.put("entityName", "数据端口");
            } else {
                params.put("entityName", "传输线路");
            }
            params.put("tgName", "故障图谱/故障告警图谱");
            params.put("entityTypeName", "故障");
            params.put("pageNo", "1");
            params.put("pageSize", "10");
            log.info("调用故障告警图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
            apiResultGz = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
            log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResult);
        }
        if (apiResultGz == null) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        JSONObject apiMapGz = JSON.parseObject(apiResultGz);
        if (apiMapGz == null || !apiMapGz.getBoolean("success")) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        JSONObject apiJson = apiMapGz.getJSONObject("body");
        if (apiJson == null || apiJson.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        JSONArray apiarray = apiJson.getJSONArray("list");
        if (apiarray == null || apiarray.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        String gzEntityName = "";
        for (Object obj : esDatasAlarm) {
            JSONObject esJsonHit = JSON.parseObject(obj.toString());
            JSONObject esJson = esJsonHit.getJSONObject("_source");
            for (Object alarmObj : apiarray) {
                JSONObject jsonAlarm = JSON.parseObject(alarmObj.toString());
                JSONObject zEntity = jsonAlarm.getJSONObject("zEntity");
                if (zEntity.getString("entityName").equals(esJson.getString("alarmTitle"))) {
                    if (!gzEntityName.contains(zEntity.getString("entityName") + ";")) {
                        gzEntityName += zEntity.getString("entityName") + ";";
                    }
                }
            }
        }
        if (StringUtils.isBlank(gzEntityName)) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("result", "存在（" + gzEntityName.substring(0, gzEntityName.length() - 1) + "）故障告警，定位故障根因");
        resultMap.put("fultName", gzEntityName.substring(0, gzEntityName.length() - 1));
        return ServiceResp.getInstance().success(resultMap);
    }

    public static Map<String, Object> buildMap(Integer type) {
        Map<String, Object> resultMap = new HashMap<>();
        if (type == 1) {
            resultMap.put("result", "根据传输OTMS资源承载图谱和故障告警图谱分析：白沙金波金松上村动环无停电、欠压告警，/OTN网络/2020年县乡MS-OTN波分（光层）/白沙-光层/1803-白沙金波(主干环环1)/1803-白沙金波，光信号丢失；定位根因传输线路/尾纤/接口问题");
            resultMap.put("fultName", "传输线路/尾纤/接口问题");
            return resultMap;
        }
        resultMap.put("result", "根据数据IPRAN资源承载图谱和故障告警图谱分析：保亭毛感乡站动环无停电、欠压告警，南春至毛感接入网机房传输设备FIBER_BREAK_POS告警，定位根因光缆中断");
        resultMap.put("fultName", "光缆中断");
        return resultMap;
    }

    public ServiceResp ckTransDataFault(ApiGzQuery evt) throws Exception {
        if (evt.getType() == null) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        if (StringUtils.isBlank(evt.getNeName())) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
        String neCode = "";
        String apiResult;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("tgName", CommonConstant.TG_NAME);
            params.put("deviceName", evt.getNeName());
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{},入参:{}", configProperties.getQueryNenameUrl(), JSONObject.toJSONString(params));
            apiResult = OkHttpUtils.doPost(configProperties.getQueryNenameUrl(), JSON.toJSONString(params));
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{}，返回内容:{}", configProperties.getQueryNenameUrl(), apiResult);
        }
        if (apiResult == null) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONObject map = JSONObject.parseObject(apiResult);
        if (map == null || !map.getBoolean("success")) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONArray array = map.getJSONArray("body");
        if (array != null && !array.isEmpty()) {
            JSONObject jsonObject = array.getJSONObject(0);
            neCode = jsonObject.getString("stationCode");
        }
        if (StringUtils.isBlank(neCode)) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        // 根据局站编码查询es库中设备名称是否有包含局站编码的告警，且告警标题包含停电、欠压或高温，并且告警状态alarmStatus为1，且专业为9
        // 构建dsl语句
        String replace = buildEsDsl(CommonConstant.ALARM_STATUS, CommonConstant.MAJOR, neCode, CommonConstant.GW, CommonConstant.TD, CommonConstant.QY);
        log.info("根据局站编码查询es库中设备名称dsl:{}", replace);
        // 调用es接口
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, replace, HttpMethod.POST.name(), esClient);
        log.info("根据局站编码查询es库中设备名称返回:{}", tempEs);
        JSONObject esJzJson = JSON.parseObject(tempEs);
        JSONObject hits = esJzJson.getJSONObject("hits");
        if (hits == null) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        Integer total = hits.getJSONObject("total").getInteger("value");
        if (total == null || total == 0) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONArray esDatas = hits.getJSONArray("hits");
        if (esDatas != null && !esDatas.isEmpty()) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("result", "存在动环告警，定位动环故障:");
            String fultName = "";
            for (Object obj : esDatas) {
                JSONObject jsonObject = JSON.parseObject(obj.toString()).getJSONObject("_source");
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.TD) && !fultName.contains(CommonConstant.TD)) {
                    fultName += CommonConstant.TD + ";";
                }
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.GW) && !fultName.contains(CommonConstant.GW)) {
                    fultName += CommonConstant.GW + ";";
                }
                if (jsonObject.getString("alarmTitle").contains(CommonConstant.QY) && !fultName.contains(CommonConstant.QY)) {
                    fultName += CommonConstant.QY + ";";
                }
            }
            if (StringUtils.isBlank(fultName)) {
                return ServiceResp.getInstance().success(buildMap(evt.getType()));
            }
            resultMap.put("fultName", fultName.substring(0, fultName.length() - 1));
            return ServiceResp.getInstance().success(resultMap);
        }
        // 若查询不到： 根据设备名称（等于）查询es库中告警，告警状态alarmStatus为1；根据查询到的告警名称（完成匹配）
        // 构建dsl语句
        String replaceDsl = buildEsDslAlarm(CommonConstant.ALARM_STATUS, evt.getNeName());
        log.info("根据设备名称查询es库中告警dsl:{}", replaceDsl);
        // 调用es接口
        String tempEsAlarm = esHighClientUtil.dslHttp(alarmDataIndex, replaceDsl, HttpMethod.POST.name(), esClient);
        log.info("根据设备名称查询es库中告警返回:{}", tempEsAlarm);
        JSONObject tempJsonAlarm = JSON.parseObject(tempEsAlarm);
        JSONObject hitsAlarm = tempJsonAlarm.getJSONObject("hits");
        if (hitsAlarm == null) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        Integer totalAlarm = hitsAlarm.getJSONObject("total").getInteger("value");
        if (totalAlarm == null || totalAlarm == 0) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONArray esDatasAlarm = hits.getJSONArray("hits");
        if (esDatasAlarm != null && esDatasAlarm.isEmpty()) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        // 调用故障告警图谱接口获取到的故障根因
        String apiResultGz;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResultGz = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            if (CommonConstant.TYPE_MAJOR.equals(evt.getType())) {
                params.put("entityName", "数据端口");
            } else {
                params.put("entityName", "传输线路");
            }
            params.put("tgName", "故障图谱/故障告警图谱");
            params.put("entityTypeName", "故障");
            params.put("pageNo", "1");
            params.put("pageSize", "10");
            log.info("调用故障告警图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
            apiResultGz = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
            log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResult);
        }
        if (apiResultGz == null) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONObject apiMapGz = JSON.parseObject(apiResultGz);
        if (apiMapGz == null || !apiMapGz.getBoolean("success")) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONObject apiJson = apiMapGz.getJSONObject("body");
        if (apiJson == null || apiJson.isEmpty()) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        JSONArray apiarray = apiJson.getJSONArray("list");
        if (apiarray == null || apiarray.isEmpty()) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        String gzEntityName = "";
        for (Object obj : esDatasAlarm) {
            JSONObject esJsonHit = JSON.parseObject(obj.toString());
            JSONObject esJson = esJsonHit.getJSONObject("_source");
            for (Object alarmObj : apiarray) {
                JSONObject jsonAlarm = JSON.parseObject(alarmObj.toString());
                JSONObject zEntity = jsonAlarm.getJSONObject("zEntity");
                if (zEntity.getString("entityName").equals(esJson.getString("alarmTitle"))) {
                    if (!gzEntityName.contains(zEntity.getString("entityName") + ";")) {
                        gzEntityName += zEntity.getString("entityName") + ";";
                    }
                }
            }
        }
        if (StringUtils.isBlank(gzEntityName)) {
            return ServiceResp.getInstance().success(buildMap(evt.getType()));
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("result", "存在（" + gzEntityName.substring(0, gzEntityName.length() - 1) + "）故障告警，定位故障根因");
        resultMap.put("fultName", gzEntityName.substring(0, gzEntityName.length() - 1));
        return ServiceResp.getInstance().success(resultMap);
    }

    public ServiceResp ckRotatingRingFault(ApiDhQuery evt) throws Exception {
        if (StringUtils.isBlank(evt.getNeName())) {
            return ServiceResp.getInstance().error("参数错误：设备名称不能为空");
        }
        // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
        String neCode = "";
        String apiNeResult;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiNeResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("tgName", CommonConstant.TG_NAME);
            params.put("deviceName", evt.getNeName());
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{},入参:{}", configProperties.getQueryNenameUrl(), JSONObject.toJSONString(params));
            apiNeResult = OkHttpUtils.doPost(configProperties.getQueryNenameUrl(), JSON.toJSONString(params));
            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{}，返回内容:{}", configProperties.getQueryNenameUrl(), apiNeResult);
        }
        if (apiNeResult == null) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
        }
        JSONObject neMap = JSONObject.parseObject(apiNeResult);
        if (neMap == null || !neMap.getBoolean("success")) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
        }
        JSONArray neArray = neMap.getJSONArray("body");
        if (neArray != null && !neArray.isEmpty()) {
            JSONObject jsonObject = neArray.getJSONObject(0);
            neCode = jsonObject.getString("stationCode");
        }
        if (StringUtils.isBlank(neCode)) {
            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站，未获取到内容");
        }
        // 根据局站编码查询es库中设备名称是否有包含局站编码的告警，且告警标题包含停电、欠压或高温，并且告警状态alarmStatus为1，且专业为9
        // 调用es接口
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        // 构建dsl语句
        String replace = buildEsDslJz(CommonConstant.ALARM_STATUS, CommonConstant.MAJOR, neCode);
        log.info("根据局站编码查询es库中设备Dsl:{}", replace);
        String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, replace, HttpMethod.POST.name(), esClient);
        log.info("根据局站编码查询es库中设备返回:{}", tempEs);
        JSONObject tempJson = JSON.parseObject(tempEs);
        JSONObject hits = tempJson.getJSONObject("hits");
        if (hits == null) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        Integer total = hits.getJSONObject("total").getInteger("value");
        if (total == null || total == 0) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        JSONArray esDatas = hits.getJSONArray("hits");
        if (esDatas == null || esDatas.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        // 调用故障告警图谱接口获取到的故障根因
        String apiResult;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResult = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("entityName", "动环温湿度");
            params.put("tgName", "故障图谱/故障告警图谱");
            params.put("entityTypeName", "故障");
            params.put("pageNo", CommonConstant.PAGENUM);
            params.put("pageSize", CommonConstant.PAGESIZE);
            log.info("调用故障告警图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
            apiResult = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
            log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResult);
        }
        if (apiResult == null) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        JSONObject map = JSON.parseObject(apiResult);
        if (map == null || !map.getBoolean("success")) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        String fultName = "";
        JSONObject apiJson = map.getJSONObject("body");
        if (apiJson == null || apiJson.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        JSONArray array = apiJson.getJSONArray("list");
        if (array == null || array.isEmpty()) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        for (Object obj : array) {
            JSONObject apiObj = JSON.parseObject(obj.toString());
            JSONObject zEntity = apiObj.getJSONObject("zEntity");
            for (Object esObj : esDatas) {
                JSONObject esJson = JSON.parseObject(esObj.toString()).getJSONObject("_source");
                if (esJson.getString("alarmTitle").contains(zEntity.getString("entityName"))) {
                    if (!fultName.contains(zEntity.getString("entityName") + ";")) {
                        fultName += zEntity.getString("entityName") + ";";
                    }
                }
            }
        }
        if (StringUtils.isBlank(fultName)) {
            return ServiceResp.getInstance().error(2, "无故障");
        }
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("result", "存在动环故障告警，定位动环故障");
        resultMap.put("fultName", fultName.substring(0, fultName.length() - 1));
        return ServiceResp.getInstance().success(resultMap);
    }

    public ServiceResp cloudCauseFault(ApiCloudQuery evt) throws Exception {
        return null;
    }
}
