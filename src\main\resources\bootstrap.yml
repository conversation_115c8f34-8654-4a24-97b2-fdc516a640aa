#密码加密串
jasypt:
  encryptor:
    password: 'ffcs_hn'

# nacos配置
spring:
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      username: nacos
#      password: 'cloud@ffcsDb4649ffcsAfg!$^'
      password: 'ENC(mWy3GtndyBy4Zqu9L84+0NAa6TtlGn4Po6SXAE4P6bJqCKnMfHdWPQ==)'
      discovery:
        # 服务注册地址
        server-addr: 192.168.35.169:32348
      config:
        # 配置中心地址
        server-addr: 192.168.35.169:32348
        # 配置文件格式
        file-extension: yml
        #超时
        timeout: 10000
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        # namespace: 38e9d288-5096-4238-b640-7733ca20baab





