
package com.ffcs.oss.config;

/**
 * Description: 魔法值
 *
 * <AUTHOR>
 */
public class CommonConstant {
    /**
     * 13.	传输线路故障根因
     */
    public static final Integer TYPE_DYNAMIC = 1;
    /**
     * 14.	数据端口故障根因
     */
    public static final Integer TYPE_MAJOR = 2;
    /**
     * 是否测试
     */
    public static final String IS_TEST = "Y";
    public static final String MAJOR = "9";
    public static final String ALARM_STATUS = "0";
    /**
     * 固定名称
     */
    public static final String TG_NAME = "OLT群障资源承载图谱";
    public static final String TD = "停电";
    public static final String GW = "高温";
    public static final String QY = "欠压";
    public static final String ZL = "直流";

    public static final String XNJ_EXCEPTION = "虚拟机异常";

    public static final String JC_EXCEPTION = "虚拟机异常";

    public static final String DW_FAULT = "大网波动";

    public static final String PLATFORM_DW_FAULT = "平台的IT/网络资源异常";

    public static final String PT = "平台";

    public static final Integer PAGESIZE = 10;
    public static final Integer PAGENUM = 1;

    /**
     * 返回的result - 可替代字段
     */
    public static final String RESULT_DH_TD_FAULT = "根据资源承载图谱和故障告警图谱分析：因#neName#；局站停电，导致传输线路故障；";

    public static final String RESULT_CS_FAULT = "根据资源承载图谱和故障告警图谱分析：因#gzEntityName#；导致传输线路故障；";

    public static final String RESULT_CELL_SITE_TD_FAULT = "根据资源承载图谱和故障告警图谱分析：因#siteSite#；站址掉电，导致数据端口故障；";

    public static final String RESULT_SJ_PORT_FAULT = "根据资源承载图谱和故障告警图谱分析：因#gzEntityName#；导致数据端口故障；";

    public static final String RESULT_DH_JZ_TD_FAULT = "结合故障告警图谱分析：因#stationName#；局站停电，导致#neName#；";

    public static final String RESULT_CLOUD_DW_FAULT = "根据资源承载图谱和故障告警图谱分析：#equipmentName#设备，网络不通；";

    public static final String RESULT_CLOUD_JC_FAULT = "根据资源承载图谱和故障告警图谱分析：#equipmentName#设备，服务进程异常；";

    public static final String RESULT_CLOUD_PTS_DW_FAULT = "根据资源承载图谱和故障告警图谱分析：#platForms#；存在大量设备网络不通；";

    public static final String RESULT_CLOUD_PT_IPS_FAULT = "根据资源承载图谱和故障告警图谱分析：云基础设施分配给#ips#的网络资源异常；";

    public static final String NO_FAULT_ROOTCAUSE = "找不到故障根因";

    public static final String NO_RESULT_CS = "根据资源承载图谱和故障告警图谱分析：因光缆中断；导致传输线路故障；";

    public static final String NO_RESULT_DK = "根据资源承载图谱和故障告警图谱分析：因光缆中断；导致数据端口故障；";

    public static final String FAULTNAME_GLZD = "光缆中断";

    // 承载故障根因分析相关常量
    public static final String CLEARANCE_REPORT_FLAG_DEFAULT = "1";
    public static final String FAULT_NAME_POWER_OUTAGE = "停电";
    public static final String FAULT_NAME_ENVIRONMENT_FAULT = "动环故障";
    public static final String FAULT_NAME_TRANSMISSION_EQUIPMENT = "下一步排查传输设备";

    // 返回结果模板
    public static final String RESULT_CARRIER_POWER_OUTAGE = "根据资源承载图谱分析：#deviceName#设备位于#stationName#，因#stationName#在#alarmTime#发生#alarmTitle#；导致#deviceName#发生故障；";
    public static final String RESULT_CARRIER_ENVIRONMENT_FAULT = "根据资源承载图谱分析：#deviceName#设备位于#stationName#，因#stationName#在#alarmTime#发生停电故障；故障识别为动环故障；";
    public static final String RESULT_CARRIER_NO_ALARM = "根据资源承载图谱分析：#deviceName#位于#stationCode#，该设备故障发生时段，#stationCode#运行正常未发生停电故障；下一步排查传输设备";
    public static final String RESULT_NO_STATION_INFO = "无设备所在局站信息";

    // 传输设备告警查询相关常量
    public static final String FAULT_NAME_CABLE_INTERRUPTION = "光缆中断";
    public static final String FAULT_NAME_BOARD_FAULT = "板卡故障";
    public static final String FAULT_NAME_TRANSMISSION_PROFESSIONAL = "传输专业故障";
    public static final String FAULT_NAME_DATA_PROFESSIONAL = "数据专业故障";
    public static final String FAULT_NAME_NEXT_CHECK_DATA_EQUIPMENT = "下一步排查数据设备";
    public static final String FAULT_NAME_MANUAL_DISPOSAL = "请转人工处置";

    // 传输设备告警查询返回结果模板
    // operationType=1时的结果模板
    public static final String RESULT_TRANSMISSION_SPECIFIC_FAULT = "根据资源承载图谱分析：#deviceName#相关的#deviceType#传输设备#peerDeviceName#，相关的传输设备#peerDeviceName#在#alarmTime#发生#alarmTitle#；导致#deviceName#发生故障；";
    public static final String RESULT_DATA_SPECIFIC_FAULT = "根据资源承载图谱分析：#deviceName#相关的#deviceType#数据设备#peerDeviceName#，相关的数据设备#peerDeviceName#在#alarmTime#发生#alarmTitle#；导致#deviceName#发生故障；";
    // operationType=0时的结果模板
    public static final String RESULT_TRANSMISSION_PROFESSIONAL_FAULT = "根据资源承载图谱分析：#deviceName#相关的#deviceType#传输设备#peerDeviceName#，相关的传输设备#peerDeviceName#在#alarmTime#发生故障；故障识别为传输专业故障；";
    public static final String RESULT_DATA_PROFESSIONAL_FAULT = "根据资源承载图谱分析：#deviceName#相关的#deviceType#数据设备#peerDeviceName#，相关的数据设备#peerDeviceName#在#alarmTime#发生故障；故障识别为数据专业故障；";
    // 未查询到告警数据时的结果模板
    public static final String RESULT_TRANSMISSION_NO_ALARM = "根据资源承载图谱分析：#deviceName#相关的#deviceType#传输设备#peerDeviceName#，该设备故障发生时段，相关的传输设备#peerDeviceName#运行正常未发生故障；下一步排查数据设备";
    public static final String RESULT_DATA_NO_ALARM = "根据资源承载图谱分析：#deviceName#相关的#deviceType#数据设备#peerDeviceName#，该设备故障发生时段，相关的数据设备#peerDeviceName#运行正常未发生故障；请转人工处置";
    // 无对端设备信息时的结果
    public static final String RESULT_NO_PEER_DEVICE = "无对端设备信息";

}
