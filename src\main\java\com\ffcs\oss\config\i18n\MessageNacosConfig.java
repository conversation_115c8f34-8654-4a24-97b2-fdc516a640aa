package com.ffcs.oss.config.i18n;


import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.Locale;
import java.util.Properties;
import java.util.concurrent.Executor;

/**
 * Nacos配置管理器
 *
 * <AUTHOR>
 * @Date 2022/8/23 11:31
 **/
@Slf4j
@Component
@ConditionalOnProperty(name = "spring.messages.localfile", havingValue = "false")
public class MessageNacosConfig {

    /**
     * 应用名称
     */
    @Value("${spring.application.name}")
    private String applicationName;
    /**
     * 命名空间
     */
    private String dNamespace;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 服务器地址
     */
    private String serverAddr;

    @Autowired
    private MessageConfig messageConfig;

    @Autowired
    private ConfigurableApplicationContext applicationContext;

    private static final String DEFAULT_GROUP = "DEFAULT_GROUP";

    private static final String DEFAULT_NAMESPACE = "public";


    @Autowired
    public void init() {
        serverAddr = applicationContext.getEnvironment().getProperty("spring.cloud.nacos.config.server-addr");
        dNamespace = applicationContext.getEnvironment().getProperty("spring.cloud.nacos.config.message-nmespace");
        groupName = applicationContext.getEnvironment().getProperty("spring.cloud.nacos.config.message-group");
        if (StringUtils.isEmpty(dNamespace)) {
            dNamespace = applicationContext.getEnvironment().getProperty("spring.cloud.nacos.config.namespace");
            if (StringUtils.isEmpty(dNamespace)) {
                dNamespace = DEFAULT_NAMESPACE;
            }
        }
        if (StringUtils.isEmpty(groupName)) {
            groupName = applicationContext.getEnvironment().getProperty("spring.cloud.nacos.config.group");
            if (StringUtils.isEmpty(groupName)) {
                groupName = DEFAULT_GROUP;
            }
        }
        initTip(null);
        initTip(Locale.CHINA);
        initTip(Locale.US);
        log.info("初始化系统参数成功!应用名称:{},Nacos地址:{},提示语命名空间:{}", applicationName, serverAddr, dNamespace);
    }

    private void initTip(Locale locale) {
        String content = null;
        String dataId = null;
        ConfigService configService = null;
        String baseName = messageConfig.getBasename();
        String fileName = baseName;
        if (baseName.contains("/")) {
            String[] spiltFileName = baseName.split("/");
            //获取最后一个文件名
            fileName = spiltFileName[spiltFileName.length - 1];
        }
        try {
            if (locale == null) {
                dataId = fileName + ".properties";
            } else {
                dataId = fileName + "_" + locale.getLanguage() + "_" + locale.getCountry() + ".properties";
            }
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, serverAddr);
            properties.put(PropertyKeyConst.NAMESPACE, dNamespace);
            configService = NacosFactory.createConfigService(properties);
            content = configService.getConfig(dataId, groupName, 5000);
            if (StringUtils.isEmpty(content)) {
                log.warn("配置内容为空,跳过初始化!dataId:{}", dataId);
                return;
            }
            log.info("初始化国际化配置!配置内容:{}", content);
            //缓存到本地
            String filePathName = dataId;
            if (baseName.contains("/")) {
                filePathName = baseName.replace(fileName, "");
            }
            saveAsFileWriter(filePathName + dataId, content);
            setListener(configService, dataId, locale);
        } catch (Exception e) {
            log.error("初始化国际化配置异常!异常信息:{}", e);
        }
    }

    /**
     * 配置文件添加监听，数据
     *
     * @param configService
     * @param dataId
     * @param locale
     * @throws com.alibaba.nacos.api.exception.NacosException
     */
    private void setListener(ConfigService configService, String dataId, Locale locale) throws com.alibaba.nacos.api.exception.NacosException {
        configService.addListener(dataId, groupName, new Listener() {
            @Override
            public void receiveConfigInfo(String configInfo) {
                log.info("接收到新的国际化配置!配置内容:{}", configInfo);
                try {
                    initTip(locale);
                } catch (Exception e) {
                    log.error("初始化国际化配置异常!异常信息:{}", e);
                }
            }

            @Override
            public Executor getExecutor() {
                return null;
            }
        });
    }

    /**
     * 配置内容保存文件
     *
     * @param fileName
     * @param content
     */
    private void saveAsFileWriter(String fileName, String content) {
        String path = System.getProperty("user.dir") + File.separator;
        try {
            fileName = path + File.separator + fileName;
            File file = new File(fileName);
            FileUtils.writeStringToFile(file, content, Charsets.UTF_8);
            log.info("国际化配置已更新!本地文件路径:{}", fileName);
        } catch (IOException e) {
            log.error("初始化国际化配置异常!本地文件路径:{}异常信息:{}", fileName, e);
        }
    }


}
