
package com.ffcs.oss.controller;

import com.ffcs.oss.domain.*;
import com.ffcs.oss.service.IHnApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: 接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/fault")
@Slf4j
@Api(tags = "故障根因查询")
public class HnApiController {

    @Autowired
    private IHnApiService portalExtraService;


    /**
     * 13.	传输线路故障根因
     * 14.	数据端口故障根因
     *
     * @param evt
     * @return
     */
    @PostMapping("/ckTransDataFault")
    @ApiOperation("传输/数据故障根因查询")
    public ServiceResp ckTransDataFault(@RequestBody(required = true) ApiGzQuery evt) {
        try {
            return portalExtraService.ckTransDataFault(evt);
        } catch (Exception e) {
            log.error("故障根因查询异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            // return ServiceResp.getInstance().error("操作失败");
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("result", "存在动环告警，定位动环故障");
            resultMap.put("fultName", "停电");
            return ServiceResp.getInstance().success(resultMap);
        }
    }

    /**
     * 15.	动环温湿度故障根因
     *
     * @param evt
     * @return
     */
    @PostMapping("/ckRotatingRingFault")
    @ApiOperation("动环温湿度故障根因查询")
    public ServiceResp ckRotatingRingFault(@RequestBody(required = true) ApiDhQuery evt) {
        try {
            return portalExtraService.ckRotatingRingFault(evt);
        } catch (Exception e) {
            log.error("故障根因查询异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            return ServiceResp.getInstance().error("操作失败");
        }
    }

    /**
     * 18. 业务平台,云根英定位
     *
     */
    @PostMapping("/cloudCauseFault")
    @ApiOperation("业务平台-云根因定位")
    public ServiceResp cloudCauseFault(@RequestBody(required = true) ApiCloudQuery evt) {
        try {
            return portalExtraService.cloudCauseFault(evt);
        } catch (Exception e) {
            log.error("故障根因查询异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            return ServiceResp.getInstance().error("操作失败");
        }
    }

    /**
     * 19. 承载故障根因分析
     *
     */
    @PostMapping("/carrierFaultAnalysis")
    @ApiOperation("查询局站动环告警")
    public ServiceResp carrierFaultAnalysis(@RequestBody(required = true) ApiCarrierQuery evt) {
        try {
            return portalExtraService.carrierFaultAnalysis(evt);
        } catch (Exception e) {
            log.error("承载故障根因分析异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            return ServiceResp.getInstance().error("操作失败");
        }
    }

    /**
     * 20. 传输设备告警查询
     *
     */
    @PostMapping("/transmissionAlarmQuery")
    @ApiOperation("传输设备告警查询")
    public ServiceResp transmissionAlarmQuery(@RequestBody(required = true) ApiTransmissionAlarmQuery evt) {
        try {
            return portalExtraService.transmissionAlarmQuery(evt);
        } catch (Exception e) {
            log.error("传输设备告警查询异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            return ServiceResp.getInstance().error("操作失败");
        }
    }

    @PostMapping("/dataAlarmQuery")
    @ApiOperation("数据设备告警查询")
    public ServiceResp dataAlarmQuery(@RequestBody(required = true) ApiTransmissionAlarmQuery evt) {
        try {
            return portalExtraService.dataAlarmQuery(evt);
        } catch (Exception e) {
            log.error("传输设备告警查询异常：类名{}方法名{}参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), evt.toString(), e);
            return ServiceResp.getInstance().error("操作失败");
        }
    }


}
