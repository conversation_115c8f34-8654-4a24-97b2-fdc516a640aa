package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("18业务平台-云根因定位")
public class ApiCloudQuery {

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "时间窗口，单位分钟")
    private Integer time;

    @ApiModelProperty(value = "告警名称1")
    private List<String> pingAlarm;

    @ApiModelProperty(value = "告警名称2")
    private List<String> downAlarm;
}
