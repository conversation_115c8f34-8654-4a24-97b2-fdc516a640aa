<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.mapper.HnApiMapper">

    <select id="getCellSite" resultType="java.lang.String">
        select cell_site from kg.cm_device cd where nameinemes = #{nameinemes}
        and cell_site is not null and cell_site != ''
    </select>

    <select id="getStationName" resultType="java.lang.String">
        select "name" from rm_area ra where alias = #{stationCode}
        and "name" is not null and "name" != ''
    </select>


</mapper>