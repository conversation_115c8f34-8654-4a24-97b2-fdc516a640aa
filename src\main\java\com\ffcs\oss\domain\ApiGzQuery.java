package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("传输/数据查询条件")
public class ApiGzQuery {
    /**
     * 类型：1传输；2数据
     */
    @ApiModelProperty(value = "类型：1传输；2数据")
    private Integer type;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String neName;

    /**
     * 告警名称 2024-12-03 新增
     */
//    @ApiModelProperty(value = "告警名称")
//    private String alarmName;
}
