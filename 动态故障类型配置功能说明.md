# 动态故障类型配置功能说明

## 功能概述

本功能实现了在确定故障类型时，TransmissionAlarmConfig和DataConfig不再局限于cableInterruption和boardFault这两个固定配置，而是支持在yml配置文件中动态配置任意数量和类型的故障类型。

## 主要特性

1. **动态配置支持**：支持在yml配置文件中定义任意故障类型
2. **优先级控制**：支持为不同故障类型设置优先级，当多种故障类型同时匹配时按优先级返回
3. **向下兼容**：保持原有的cableInterruption和boardFault配置方式，确保现有业务不受影响
4. **灵活扩展**：可以轻松添加新的故障类型而无需修改代码

## 配置结构

### 1. 传输专业配置 (transmission)

```yaml
alarm:
  transmission:
    # 向下兼容的固定配置
    cableInterruption:
      - "光缆中断"
      - "链路故障"
    boardFault:
      - "板卡故障"
      - "硬件故障"
    
    # 新增的动态配置（推荐使用）
    faultTypes:
      cableInterruption:
        - "光缆中断"
        - "链路故障"
      boardFault:
        - "板卡故障"
        - "硬件故障"
      powerFault:
        - "停电告警"
        - "电源故障"
      networkFault:
        - "网络异常"
        - "路由故障"
      temperatureFault:
        - "高温告警"
        - "温度异常"
    
    # 故障类型优先级配置
    priorities:
      powerFault: 1           # 电源故障优先级最高
      cableInterruption: 2    # 光缆中断次之
      temperatureFault: 3     # 温度故障
      networkFault: 4         # 网络故障
      boardFault: 5           # 板卡故障优先级最低
```

### 2. 数据专业配置 (data-config)

```yaml
alarm:
  data-config:
    # 向下兼容的固定配置
    cableInterruption:
      - "光缆中断"
      - "链路故障"
    boardFault:
      - "板卡故障"
      - "硬件故障"
    
    # 新增的动态配置（推荐使用）
    faultTypes:
      cableInterruption:
        - "光缆中断"
        - "链路故障"
      softwareFault:
        - "软件异常"
        - "程序崩溃"
      configurationFault:
        - "配置错误"
        - "参数异常"
      performanceFault:
        - "性能下降"
        - "响应超时"
    
    # 故障类型优先级配置
    priorities:
      cableInterruption: 1      # 光缆中断优先级最高
      configurationFault: 2     # 配置故障次之
      softwareFault: 3          # 软件故障
      performanceFault: 4       # 性能故障
```

## 支持的故障类型

系统预定义了以下故障类型常量，可以直接在配置中使用：

| 故障类型Key | 对应常量 | 中文名称 |
|------------|----------|----------|
| cableInterruption | FAULT_NAME_CABLE_INTERRUPTION | 光缆中断 |
| boardFault | FAULT_NAME_BOARD_FAULT | 板卡故障 |
| powerFault | FAULT_NAME_POWER_OUTAGE | 停电 |
| temperatureFault | FAULT_NAME_ENVIRONMENT_FAULT | 动环故障 |
| networkFault | FAULT_NAME_NETWORK_FAULT | 网络故障 |
| hardwareFault | FAULT_NAME_HARDWARE_FAULT | 硬件故障 |
| softwareFault | FAULT_NAME_SOFTWARE_FAULT | 软件故障 |
| configurationFault | FAULT_NAME_CONFIGURATION_FAULT | 配置故障 |
| performanceFault | FAULT_NAME_PERFORMANCE_FAULT | 性能故障 |
| securityFault | FAULT_NAME_SECURITY_FAULT | 安全故障 |
| communicationFault | FAULT_NAME_COMMUNICATION_FAULT | 通信故障 |
| protocolFault | FAULT_NAME_PROTOCOL_FAULT | 协议故障 |

## 工作原理

1. **配置加载**：系统启动时从nacos或本地配置文件加载故障类型配置
2. **告警匹配**：当接收到告警时，系统遍历所有配置的故障类型，检查告警标题是否包含配置的关键词
3. **优先级判断**：如果匹配到多种故障类型，根据priorities配置选择优先级最高的（数字越小优先级越高）
4. **结果返回**：返回对应的故障类型常量名称

## 代码变更

### 1. AlarmNameConfig类增强

- 新增`faultTypes`和`priorities`字段支持动态配置
- 新增辅助方法`getAllTransmissionFaultTypes()`、`getAllDataFaultTypes()`等
- 保持向下兼容，原有的`cableInterruption`和`boardFault`字段仍然有效

### 2. 故障类型判断逻辑优化

- 重构`determineFaultType()`和`dataDetermineFaultType()`方法
- 新增通用的`determineFaultTypeByConfig()`方法支持动态配置
- 新增`convertFaultTypeToConstant()`方法进行故障类型转换

### 3. 常量定义扩展

在`CommonConstant`类中新增了多种故障类型常量，便于扩展和维护。

## 使用示例

### 1. 添加新的故障类型

如需添加"数据库故障"类型：

```yaml
alarm:
  transmission:
    faultTypes:
      databaseFault:
        - "数据库连接失败"
        - "数据库超时"
        - "数据库异常"
    priorities:
      databaseFault: 2  # 设置优先级
```

### 2. 修改优先级

调整故障类型优先级：

```yaml
alarm:
  transmission:
    priorities:
      powerFault: 1           # 最高优先级
      cableInterruption: 2    
      databaseFault: 3        # 新增的数据库故障
      networkFault: 4         
      boardFault: 5           # 最低优先级
```

## 测试验证

项目提供了完整的测试用例`DynamicAlarmConfigTest`，包括：

- 动态配置加载测试
- 单一故障类型判断测试
- 多故障类型优先级测试
- 向下兼容性测试
- 默认值处理测试

## 注意事项

1. **配置优先级**：动态配置`faultTypes`的优先级高于固定配置`cableInterruption`和`boardFault`
2. **优先级数值**：priorities中的数字越小表示优先级越高
3. **关键词匹配**：告警标题匹配采用包含匹配（contains），建议配置具有区分度的关键词
4. **配置刷新**：使用了`@RefreshScope`注解，支持nacos配置动态刷新
5. **向下兼容**：现有的固定配置方式仍然有效，可以逐步迁移到动态配置

## 扩展建议

1. **添加新故障类型**：在`CommonConstant`中定义新的常量，在`convertFaultTypeToConstant`方法中添加映射
2. **自定义匹配逻辑**：可以扩展匹配算法，支持正则表达式或更复杂的匹配规则
3. **配置验证**：可以添加配置验证逻辑，确保配置的有效性
4. **监控告警**：可以添加配置变更监控，及时发现配置问题
