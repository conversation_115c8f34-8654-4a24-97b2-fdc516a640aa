# 动态故障类型配置功能说明

## 功能概述

本功能实现了在确定故障类型时，TransmissionAlarmConfig和DataConfig不再局限于cableInterruption和boardFault这两个固定配置，而是支持在yml配置文件中动态配置任意数量和类型的故障类型。

## 主要特性

1. **动态配置支持**：支持在yml配置文件中定义任意故障类型
2. **优先级控制**：支持为不同故障类型设置优先级，当多种故障类型同时匹配时按优先级返回
3. **向下兼容**：保持原有的cableInterruption和boardFault配置方式，确保现有业务不受影响
4. **灵活扩展**：可以轻松添加新的故障类型而无需修改代码
5. **映射配置化**：故障类型到显示名称的映射完全配置化，无需在代码中硬编码
6. **自定义显示名称**：支持为任意故障类型配置自定义的显示名称

## 配置结构

### 1. 故障类型映射配置 (faultTypeMapping)

```yaml
alarm:
  # 故障类型映射配置（全局配置，支持完全自定义故障类型显示名称）
  faultTypeMapping:
    # 基础故障类型映射
    cableInterruption: "光缆中断"
    boardFault: "板卡故障"
    powerFault: "停电"
    temperatureFault: "动环故障"

    # 网络相关故障类型映射
    networkFault: "网络故障"
    communicationFault: "通信故障"
    protocolFault: "协议故障"

    # 设备相关故障类型映射
    hardwareFault: "硬件故障"
    softwareFault: "软件故障"
    configurationFault: "配置故障"
    performanceFault: "性能故障"
    securityFault: "安全故障"

    # 自定义业务故障类型映射示例
    databaseFault: "数据库故障"
    paymentFault: "支付系统故障"
    authFault: "认证系统故障"
    customBusinessFault: "业务系统故障"
```

### 2. 传输专业配置 (transmission)

```yaml
alarm:
  transmission:
    # 向下兼容的固定配置
    cableInterruption:
      - "光缆中断"
      - "链路故障"
    boardFault:
      - "板卡故障"
      - "硬件故障"
    
    # 新增的动态配置（推荐使用）
    faultTypes:
      cableInterruption:
        - "光缆中断"
        - "链路故障"
      boardFault:
        - "板卡故障"
        - "硬件故障"
      powerFault:
        - "停电告警"
        - "电源故障"
      networkFault:
        - "网络异常"
        - "路由故障"
      temperatureFault:
        - "高温告警"
        - "温度异常"
    
    # 故障类型优先级配置
    priorities:
      powerFault: 1           # 电源故障优先级最高
      cableInterruption: 2    # 光缆中断次之
      temperatureFault: 3     # 温度故障
      networkFault: 4         # 网络故障
      boardFault: 5           # 板卡故障优先级最低
```

### 3. 数据专业配置 (data-config)

```yaml
alarm:
  data-config:
    # 向下兼容的固定配置
    cableInterruption:
      - "光缆中断"
      - "链路故障"
    boardFault:
      - "板卡故障"
      - "硬件故障"
    
    # 新增的动态配置（推荐使用）
    faultTypes:
      cableInterruption:
        - "光缆中断"
        - "链路故障"
      softwareFault:
        - "软件异常"
        - "程序崩溃"
      configurationFault:
        - "配置错误"
        - "参数异常"
      performanceFault:
        - "性能下降"
        - "响应超时"
    
    # 故障类型优先级配置
    priorities:
      cableInterruption: 1      # 光缆中断优先级最高
      configurationFault: 2     # 配置故障次之
      softwareFault: 3          # 软件故障
      performanceFault: 4       # 性能故障
```

## 故障类型配置方式

### 1. 完全配置化的故障类型映射

现在系统支持完全配置化的故障类型映射，您可以：

- **自定义故障类型Key**：可以使用任意有意义的Key作为故障类型标识
- **自定义显示名称**：可以为每个故障类型配置任意的中文显示名称
- **动态添加新类型**：无需修改代码，直接在配置文件中添加新的故障类型

### 2. 默认故障类型映射

如果不配置`faultTypeMapping`，系统会使用以下默认映射：

| 故障类型Key | 默认显示名称 | 说明 |
|------------|-------------|------|
| cableInterruption | 光缆中断 | 光缆、光纤相关故障 |
| boardFault | 板卡故障 | 板卡、硬件相关故障 |
| powerFault | 停电 | 电源、供电相关故障 |
| temperatureFault | 动环故障 | 温度、环境相关故障 |
| networkFault | 网络故障 | 网络连接相关故障 |
| hardwareFault | 硬件故障 | 通用硬件故障 |
| softwareFault | 软件故障 | 软件、程序相关故障 |
| configurationFault | 配置故障 | 配置、参数相关故障 |
| performanceFault | 性能故障 | 性能、响应相关故障 |
| securityFault | 安全故障 | 安全、权限相关故障 |
| communicationFault | 通信故障 | 通信、协议相关故障 |
| protocolFault | 协议故障 | 协议解析相关故障 |

## 工作原理

1. **配置加载**：系统启动时从nacos或本地配置文件加载故障类型配置和映射配置
2. **告警匹配**：当接收到告警时，系统遍历所有配置的故障类型，检查告警标题是否包含配置的关键词
3. **优先级判断**：如果匹配到多种故障类型，根据priorities配置选择优先级最高的（数字越小优先级越高）
4. **映射转换**：将匹配到的故障类型Key通过faultTypeMapping配置转换为对应的显示名称
5. **结果返回**：返回配置的故障类型显示名称，如果没有配置映射则返回默认值

## 代码变更

### 1. AlarmNameConfig类增强

- 新增`faultTypes`和`priorities`字段支持动态配置
- 新增辅助方法`getAllTransmissionFaultTypes()`、`getAllDataFaultTypes()`等
- 保持向下兼容，原有的`cableInterruption`和`boardFault`字段仍然有效

### 2. 故障类型判断逻辑优化

- 重构`determineFaultType()`和`dataDetermineFaultType()`方法
- 新增通用的`determineFaultTypeByConfig()`方法支持动态配置
- 新增`convertFaultTypeToConstant()`方法进行故障类型转换

### 3. 故障类型映射配置

- 新增`faultTypeMapping`字段支持故障类型到显示名称的映射配置
- 新增`getFaultTypeMapping()`方法获取故障类型映射，支持默认映射

### 4. 故障类型转换逻辑重构

- 重构`convertFaultTypeToConstant()`方法，从硬编码的switch语句改为从配置中读取映射
- 支持完全配置化的故障类型映射，无需修改代码即可添加新的故障类型

## 使用示例

### 1. 添加新的故障类型

如需添加"数据库故障"类型：

```yaml
alarm:
  # 1. 首先在故障类型映射中定义显示名称
  faultTypeMapping:
    databaseFault: "数据库故障"

  # 2. 然后在专业配置中添加故障类型和告警关键词
  transmission:
    faultTypes:
      databaseFault:
        - "数据库连接失败"
        - "数据库超时"
        - "数据库异常"
    priorities:
      databaseFault: 2  # 设置优先级
```

### 2. 自定义故障类型显示名称

可以为任意故障类型配置自定义的显示名称：

```yaml
alarm:
  faultTypeMapping:
    # 使用业务相关的显示名称
    paymentSystemFault: "支付系统异常"
    orderProcessFault: "订单处理故障"
    userAuthFault: "用户认证失败"
    # 使用更具体的描述
    networkConnectivityFault: "网络连接中断"
    databasePerformanceFault: "数据库性能异常"
```

### 3. 修改优先级

调整故障类型优先级：

```yaml
alarm:
  transmission:
    priorities:
      powerFault: 1           # 最高优先级
      cableInterruption: 2
      databaseFault: 3        # 新增的数据库故障
      networkFault: 4
      boardFault: 5           # 最低优先级
```

## 测试验证

项目提供了完整的测试用例`DynamicAlarmConfigTest`，包括：

- 动态配置加载测试
- 单一故障类型判断测试
- 多故障类型优先级测试
- 向下兼容性测试
- 默认值处理测试
- 故障类型映射配置测试
- 自定义故障类型完整流程测试

## 注意事项

1. **配置优先级**：动态配置`faultTypes`的优先级高于固定配置`cableInterruption`和`boardFault`
2. **优先级数值**：priorities中的数字越小表示优先级越高
3. **关键词匹配**：告警标题匹配采用包含匹配（contains），建议配置具有区分度的关键词
4. **配置刷新**：使用了`@RefreshScope`注解，支持nacos配置动态刷新
5. **向下兼容**：现有的固定配置方式仍然有效，可以逐步迁移到动态配置
6. **映射配置**：`faultTypeMapping`是全局配置，对所有专业的故障类型都生效
7. **自定义显示名称**：可以使用任意中文名称作为故障类型的显示名称，不受代码中常量限制

## 扩展建议

1. **添加新故障类型**：直接在`faultTypeMapping`配置中添加新的故障类型映射，无需修改代码
2. **自定义匹配逻辑**：可以扩展匹配算法，支持正则表达式或更复杂的匹配规则
3. **配置验证**：可以添加配置验证逻辑，确保配置的有效性
4. **监控告警**：可以添加配置变更监控，及时发现配置问题
5. **国际化支持**：可以扩展支持多语言的故障类型显示名称
6. **动态配置管理**：可以开发配置管理界面，方便运维人员动态调整故障类型配置

## 总结

通过本次改进，系统实现了：

- ✅ **完全配置化**：故障类型的定义、匹配规则、优先级、显示名称全部可配置
- ✅ **零代码扩展**：添加新故障类型无需修改任何代码，只需配置即可
- ✅ **灵活映射**：支持任意自定义的故障类型显示名称
- ✅ **向下兼容**：保持与现有系统的完全兼容
- ✅ **动态刷新**：支持配置的动态刷新，无需重启应用

这样的设计使得系统具有极高的灵活性和可扩展性，能够快速适应不断变化的业务需求。
