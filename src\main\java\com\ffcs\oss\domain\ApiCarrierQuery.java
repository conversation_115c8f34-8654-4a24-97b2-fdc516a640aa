package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("承载故障根因查询条件")
public class ApiCarrierQuery {
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备IP
     */
    @ApiModelProperty(value = "设备IP")
    private String deviceIp;

    /**
     * 告警发生时间
     */
    @ApiModelProperty(value = "告警发生时间")
    private String startTime;

    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String specialty;

    /**
     * 操作类型：0-动环故障，1-停电
     */
    @ApiModelProperty(value = "操作类型：0-动环故障，1-停电")
    private Integer operationType;

    /**
     * 图谱名称
     */
    @ApiModelProperty(value = "图谱名称")
    private String tgName;

    /**
     * 查询类型：0-图谱查询对端设备，1-查询传输设备告警
     */
    @ApiModelProperty(value = "查询类型：0-图谱查询对端设备，1-查询传输设备告警")
    private Integer searchType;

    private String clearancereportflag;
}
