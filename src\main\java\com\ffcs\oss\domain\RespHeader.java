package com.ffcs.oss.domain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("响应头")
public class RespHeader implements Serializable {
    private static final long serialVersionUID = -4761768477180935479L;
    @ApiModelProperty("响应时间")
    private String resTime;
    @ApiModelProperty("响应流水")
    private String ticket;
    @ApiModelProperty("响应编码:-1失败,0成功")
    private Integer respCode;
    @ApiModelProperty("响应信息描述")
    private String respMsg;

    public RespHeader() {
    }

    public String getResTime() {
        return this.resTime;
    }

    public void setResTime(String resTime) {
        this.resTime = resTime;
    }

    public String getTicket() {
        return this.ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public Integer getRespCode() {
        return this.respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return this.respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    @Override
    public String toString() {
        return "RespHeader{resTime='" + this.resTime + '\'' + ", ticket='" + this.ticket + '\'' + ", respCode=" + this.respCode + ", respMsg='" + this.respMsg + '\'' + '}';
    }
}

