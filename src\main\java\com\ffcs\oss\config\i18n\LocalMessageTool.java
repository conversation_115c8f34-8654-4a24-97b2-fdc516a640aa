package com.ffcs.oss.config.i18n;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;



/**
 * 国际化配置获取工具类
 *
 * <AUTHOR>
 * @Date 2022/8/23 11:31
 **/
@Slf4j
@Component
public class LocalMessageTool {


    private static MessageSource messageSource;

    public LocalMessageTool(MessageSource messageSource) {
        LocalMessageTool.messageSource = messageSource;
    }

    /**
     * 获取国际化翻译值()
     * 调用方式：
     * LocalMessageUtil.getMessage("keyName",new Object[]{args1,args2,...})
     * LocalMessageUtil.getMessage("keyName")
     * LocalMessageUtil.getMessage("keyName","str")
     *
     * @param msgKey
     * @param args
     * @return
     */
    public static String getMessage(String msgKey, Object... args) {
        return getLocalMessage(msgKey, args, msgKey);
    }

    /**
     * 根据国际化KEY获取国际化信息
     *
     * @param code 国际化KEY
     * @return
     * <AUTHOR>
     */
    public static String getLocalMessage(String code) {
        return getLocalMessage(code, null);
    }

    /**
     * 根据国际化KEY获取国际化信息
     *
     * @param code 国际化KEY
     * @param args 国际化KEY的值中参数占位符的值
     * @return
     * <AUTHOR>
     */
    public static String getLocalMessage(String code, Object[] args) {
        return getLocalMessage(code, args, code);
    }

    /**
     * 根据国际化KEY获取国际化信息
     *
     * @param code       国际化KEY
     * @param args       国际化KEY的值中参数占位符的值
     * @param defaultMsg 默认值
     * @return
     * <AUTHOR>
     */
    public static String getLocalMessage(String code, Object[] args, String defaultMsg) {

        try {
            Locale locale = LocaleContextHolder.getLocale();
            return messageSource.getMessage(code, args, defaultMsg, locale);
        } catch (NoSuchMessageException e) {
            log.error("获取配置异常!异常信息:{}", e);
        }
        return null;
    }


}

