package com.ffcs.oss.config;

import com.ffcs.oss.domain.AlarmResponse;
import com.ffcs.oss.service.IHnApiService;
import com.ffcs.oss.service.impl.HnApiServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 动态告警配置功能测试
 */
@SpringBootTest
@SpringJUnitConfig
public class DynamicAlarmConfigTest {

    @Resource
    private AlarmNameConfig alarmNameConfig;

    @Resource
    private IHnApiService hnApiService;

    private HnApiServiceImpl hnApiServiceImpl;

    @BeforeEach
    public void setUp() {
        hnApiServiceImpl = (HnApiServiceImpl) hnApiService;
        setupTestConfig();
    }

    /**
     * 设置测试配置
     */
    private void setupTestConfig() {
        // 设置传输专业动态配置
        AlarmNameConfig.TransmissionAlarmConfig transmissionConfig = new AlarmNameConfig.TransmissionAlarmConfig();
        
        // 设置动态故障类型
        Map<String, List<String>> transmissionFaultTypes = new HashMap<>();
        transmissionFaultTypes.put("cableInterruption", Arrays.asList("光缆中断", "链路故障"));
        transmissionFaultTypes.put("boardFault", Arrays.asList("板卡故障", "硬件故障"));
        transmissionFaultTypes.put("powerFault", Arrays.asList("停电告警", "电源故障"));
        transmissionFaultTypes.put("networkFault", Arrays.asList("网络异常", "路由故障"));
        transmissionConfig.setFaultTypes(transmissionFaultTypes);
        
        // 设置优先级
        Map<String, Integer> transmissionPriorities = new HashMap<>();
        transmissionPriorities.put("powerFault", 1);
        transmissionPriorities.put("cableInterruption", 2);
        transmissionPriorities.put("networkFault", 3);
        transmissionPriorities.put("boardFault", 4);
        transmissionConfig.setPriorities(transmissionPriorities);
        
        alarmNameConfig.setTransmission(transmissionConfig);

        // 设置数据专业动态配置
        AlarmNameConfig.DataConfig dataConfig = new AlarmNameConfig.DataConfig();
        
        Map<String, List<String>> dataFaultTypes = new HashMap<>();
        dataFaultTypes.put("cableInterruption", Arrays.asList("光缆中断", "链路故障"));
        dataFaultTypes.put("softwareFault", Arrays.asList("软件异常", "程序崩溃"));
        dataFaultTypes.put("configurationFault", Arrays.asList("配置错误", "参数异常"));
        dataConfig.setFaultTypes(dataFaultTypes);
        
        Map<String, Integer> dataPriorities = new HashMap<>();
        dataPriorities.put("cableInterruption", 1);
        dataPriorities.put("configurationFault", 2);
        dataPriorities.put("softwareFault", 3);
        dataConfig.setPriorities(dataPriorities);
        
        alarmNameConfig.setDataConfig(dataConfig);

        // 设置故障类型映射配置
        Map<String, String> faultTypeMapping = new HashMap<>();
        faultTypeMapping.put("cableInterruption", "光缆中断");
        faultTypeMapping.put("boardFault", "板卡故障");
        faultTypeMapping.put("powerFault", "停电");
        faultTypeMapping.put("networkFault", "网络故障");
        faultTypeMapping.put("softwareFault", "软件故障");
        faultTypeMapping.put("configurationFault", "配置故障");
        faultTypeMapping.put("customFault", "自定义故障类型");
        alarmNameConfig.setFaultTypeMapping(faultTypeMapping);
    }

    /**
     * 测试获取传输专业所有故障类型
     */
    @Test
    public void testGetAllTransmissionFaultTypes() {
        Map<String, List<String>> allFaultTypes = alarmNameConfig.getAllTransmissionFaultTypes();
        
        assertNotNull(allFaultTypes);
        assertTrue(allFaultTypes.containsKey("cableInterruption"));
        assertTrue(allFaultTypes.containsKey("boardFault"));
        assertTrue(allFaultTypes.containsKey("powerFault"));
        assertTrue(allFaultTypes.containsKey("networkFault"));
        
        assertEquals(Arrays.asList("光缆中断", "链路故障"), allFaultTypes.get("cableInterruption"));
        assertEquals(Arrays.asList("停电告警", "电源故障"), allFaultTypes.get("powerFault"));
    }

    /**
     * 测试获取数据专业所有故障类型
     */
    @Test
    public void testGetAllDataFaultTypes() {
        Map<String, List<String>> allFaultTypes = alarmNameConfig.getAllDataFaultTypes();
        
        assertNotNull(allFaultTypes);
        assertTrue(allFaultTypes.containsKey("cableInterruption"));
        assertTrue(allFaultTypes.containsKey("softwareFault"));
        assertTrue(allFaultTypes.containsKey("configurationFault"));
        
        assertEquals(Arrays.asList("软件异常", "程序崩溃"), allFaultTypes.get("softwareFault"));
        assertEquals(Arrays.asList("配置错误", "参数异常"), allFaultTypes.get("configurationFault"));
    }

    /**
     * 测试传输专业优先级配置
     */
    @Test
    public void testGetTransmissionPriorities() {
        Map<String, Integer> priorities = alarmNameConfig.getTransmissionPriorities();
        
        assertNotNull(priorities);
        assertEquals(Integer.valueOf(1), priorities.get("powerFault"));
        assertEquals(Integer.valueOf(2), priorities.get("cableInterruption"));
        assertEquals(Integer.valueOf(3), priorities.get("networkFault"));
        assertEquals(Integer.valueOf(4), priorities.get("boardFault"));
    }

    /**
     * 测试数据专业优先级配置
     */
    @Test
    public void testGetDataPriorities() {
        Map<String, Integer> priorities = alarmNameConfig.getDataPriorities();
        
        assertNotNull(priorities);
        assertEquals(Integer.valueOf(1), priorities.get("cableInterruption"));
        assertEquals(Integer.valueOf(2), priorities.get("configurationFault"));
        assertEquals(Integer.valueOf(3), priorities.get("softwareFault"));
    }

    /**
     * 测试动态故障类型判断 - 单一故障类型
     */
    @Test
    public void testDetermineFaultTypeSingle() throws Exception {
        // 创建测试告警数据
        List<AlarmResponse.AlarmRecord> alarms = new ArrayList<>();
        AlarmResponse.AlarmRecord alarm = new AlarmResponse.AlarmRecord();
        alarm.setAlarmtitle("光缆中断告警");
        alarms.add(alarm);

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "determineFaultTypeByConfig", alarms, "transmission");
        
        assertEquals(CommonConstant.FAULT_NAME_CABLE_INTERRUPTION, result);
    }

    /**
     * 测试动态故障类型判断 - 多故障类型优先级
     */
    @Test
    public void testDetermineFaultTypeMultipleWithPriority() throws Exception {
        // 创建测试告警数据 - 包含多种故障类型
        List<AlarmResponse.AlarmRecord> alarms = new ArrayList<>();
        
        AlarmResponse.AlarmRecord alarm1 = new AlarmResponse.AlarmRecord();
        alarm1.setAlarmtitle("板卡故障告警");
        alarms.add(alarm1);
        
        AlarmResponse.AlarmRecord alarm2 = new AlarmResponse.AlarmRecord();
        alarm2.setAlarmtitle("停电告警");  // 优先级更高
        alarms.add(alarm2);

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "determineFaultTypeByConfig", alarms, "transmission");
        
        // 应该返回优先级更高的电源故障
        assertEquals(CommonConstant.FAULT_NAME_POWER_OUTAGE, result);
    }

    /**
     * 测试数据专业故障类型判断
     */
    @Test
    public void testDataDetermineFaultType() throws Exception {
        // 创建测试告警数据
        List<AlarmResponse.AlarmRecord> alarms = new ArrayList<>();
        AlarmResponse.AlarmRecord alarm = new AlarmResponse.AlarmRecord();
        alarm.setAlarmtitle("软件异常告警");
        alarms.add(alarm);

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "determineFaultTypeByConfig", alarms, "data");
        
        assertEquals(CommonConstant.FAULT_NAME_SOFTWARE_FAULT, result);
    }

    /**
     * 测试未匹配故障类型返回默认值
     */
    @Test
    public void testDetermineFaultTypeDefault() throws Exception {
        // 创建测试告警数据 - 不匹配任何配置的故障类型
        List<AlarmResponse.AlarmRecord> alarms = new ArrayList<>();
        AlarmResponse.AlarmRecord alarm = new AlarmResponse.AlarmRecord();
        alarm.setAlarmtitle("未知故障告警");
        alarms.add(alarm);

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "determineFaultTypeByConfig", alarms, "transmission");
        
        assertEquals(CommonConstant.FAULT_NAME_TRANSMISSION_PROFESSIONAL, result);
    }

    /**
     * 测试故障类型转换
     */
    @Test
    public void testConvertFaultTypeToConstant() throws Exception {
        // 使用反射调用私有方法
        String result1 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "cableInterruption", "default");
        assertEquals(CommonConstant.FAULT_NAME_CABLE_INTERRUPTION, result1);

        String result2 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "boardFault", "default");
        assertEquals(CommonConstant.FAULT_NAME_BOARD_FAULT, result2);

        String result3 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "unknownFault", "default");
        assertEquals("default", result3);
    }

    /**
     * 测试故障类型映射配置
     */
    @Test
    public void testGetFaultTypeMapping() {
        Map<String, String> mapping = alarmNameConfig.getFaultTypeMapping();

        assertNotNull(mapping);
        assertEquals("光缆中断", mapping.get("cableInterruption"));
        assertEquals("板卡故障", mapping.get("boardFault"));
        assertEquals("停电", mapping.get("powerFault"));
        assertEquals("网络故障", mapping.get("networkFault"));
        assertEquals("软件故障", mapping.get("softwareFault"));
        assertEquals("配置故障", mapping.get("configurationFault"));
        assertEquals("自定义故障类型", mapping.get("customFault"));
    }

    /**
     * 测试使用配置映射的故障类型转换
     */
    @Test
    public void testConvertFaultTypeWithMapping() throws Exception {
        // 测试配置中存在的故障类型
        String result1 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "cableInterruption", "default");
        assertEquals("光缆中断", result1);

        String result2 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "customFault", "default");
        assertEquals("自定义故障类型", result2);

        // 测试配置中不存在的故障类型
        String result3 = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "unknownFault", "default");
        assertEquals("default", result3);
    }

    /**
     * 测试动态添加故障类型映射
     */
    @Test
    public void testDynamicFaultTypeMapping() throws Exception {
        // 动态添加新的故障类型映射
        Map<String, String> currentMapping = alarmNameConfig.getFaultTypeMapping();
        currentMapping.put("newFaultType", "新故障类型");

        // 测试新添加的故障类型映射
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "convertFaultTypeToConstant", "newFaultType", "default");
        assertEquals("新故障类型", result);
    }

    /**
     * 测试完整的动态配置流程 - 包含自定义故障类型
     */
    @Test
    public void testCompleteCustomFaultTypeFlow() throws Exception {
        // 1. 添加自定义故障类型到传输配置
        Map<String, List<String>> transmissionFaultTypes = alarmNameConfig.getAllTransmissionFaultTypes();
        transmissionFaultTypes.put("customBusinessFault", Arrays.asList("业务系统异常", "业务流程中断"));

        // 2. 设置自定义故障类型的优先级
        Map<String, Integer> priorities = alarmNameConfig.getTransmissionPriorities();
        priorities.put("customBusinessFault", 1); // 设置为最高优先级

        // 3. 添加故障类型映射
        Map<String, String> mapping = alarmNameConfig.getFaultTypeMapping();
        mapping.put("customBusinessFault", "业务系统故障");

        // 4. 创建包含自定义故障类型的告警
        List<AlarmResponse.AlarmRecord> alarms = new ArrayList<>();
        AlarmResponse.AlarmRecord alarm = new AlarmResponse.AlarmRecord();
        alarm.setAlarmtitle("业务系统异常告警");
        alarms.add(alarm);

        // 5. 测试故障类型判断
        String result = (String) ReflectionTestUtils.invokeMethod(
                hnApiServiceImpl, "determineFaultTypeByConfig", alarms, "transmission");

        assertEquals("业务系统故障", result);
    }
}
