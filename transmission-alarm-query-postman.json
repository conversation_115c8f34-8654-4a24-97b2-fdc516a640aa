{"info": {"_postman_id": "transmission-alarm-query-api", "name": "传输设备告警查询API测试", "description": "传输设备告警查询功能的Postman测试用例集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 图谱查询对端设备 (searchType=0)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2025-07-18 18:48:07\",\n    \"specialty\": \"传输\",\n    \"operationType\": 1,\n    \"searchType\": 0,\n    \"deviceType\": \"基站设备\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试图谱查询对端设备功能，searchType=0"}}, {"name": "2. 查询传输设备告警-具体故障类型 (searchType=1, operationType=1)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2025-07-18 18:48:07\",\n    \"specialty\": \"传输\",\n    \"operationType\": 1,\n    \"searchType\": 1,\n    \"deviceType\": \"基站设备\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试查询传输设备告警功能，返回具体故障类型"}}, {"name": "3. 查询传输设备告警-传输专业故障 (searchType=1, operationType=0)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2025-07-18 18:48:07\",\n    \"specialty\": \"传输\",\n    \"operationType\": 0,\n    \"searchType\": 1,\n    \"deviceType\": \"基站设备\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试查询传输设备告警功能，返回传输专业故障"}}, {"name": "4. 仅使用设备名称查询 (无IP)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"startTime\": \"2025-07-18 18:48:07\",\n    \"specialty\": \"传输\",\n    \"operationType\": 1,\n    \"searchType\": 1,\n    \"deviceType\": \"基站设备\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试仅使用设备名称查询的情况"}}, {"name": "5. 无专业过滤查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2025-07-18 18:48:07\",\n    \"operationType\": 1,\n    \"searchType\": 1,\n    \"deviceType\": \"基站设备\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试不传入specialty参数的情况，应该返回全量告警"}}, {"name": "6. 参数校验测试 - 缺少必填参数", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"海口基站设备001\",\n    \"startTime\": \"2025-07-18 18:48:07\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/transmissionAlarmQuery", "host": ["{{baseUrl}}"], "path": ["api", "fault", "transmissionAlarmQuery"]}, "description": "测试缺少必填参数的情况，应该返回参数错误"}}, {"name": "7. 测试接口 - 图谱查询对端设备模拟", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceIp\": \"*************\",\n    \"tgName\": \"传输设备图谱\"\n}"}, "url": {"raw": "{{baseUrl}}/api/fault/get6", "host": ["{{baseUrl}}"], "path": ["api", "fault", "get6"]}, "description": "测试图谱查询对端设备的模拟接口"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:9100", "description": "API基础地址"}]}