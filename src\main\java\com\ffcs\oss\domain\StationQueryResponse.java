package com.ffcs.oss.domain;

import lombok.Data;

import java.util.List;

/**
 * 图谱能力查询局站响应实体
 */
@Data
public class StationQueryResponse {
    private StationResponseHead head;
    private List<StationInfo> body;
    private Boolean success;

    @Data
    public static class StationResponseHead {
        private String resTime;
        private String ticket;
        private Integer respCode;
        private String respMsg;
    }

    @Data
    public static class StationInfo {
        private String stationCode;
        private String stationName;
        private String stationId;
    }
}
