{"info": {"_postman_id": "carrier-fault-analysis-collection", "name": "承载故障根因分析接口测试", "description": "海南API系统承载故障根因分析功能的接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "承载故障根因分析-停电场景", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"specialty\": \"无线(中兴5G)\",\n    \"operationType\": 1,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试停电场景的故障根因分析（operationType=1）"}}, {"name": "承载故障根因分析-动环故障场景", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"specialty\": \"无线(中兴5G)\",\n    \"operationType\": 0,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试动环故障场景的故障根因分析（operationType=0）"}}, {"name": "承载故障根因分析-仅使用设备名称", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"specialty\": \"无线(中兴5G)\",\n    \"operationType\": 1,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试仅使用设备名称查询的场景"}}, {"name": "承载故障根因分析-仅使用设备IP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"specialty\": \"无线(中兴5G)\",\n    \"operationType\": 1,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试仅使用设备IP查询的场景"}}, {"name": "承载故障根因分析-参数校验测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"specialty\": \"无线(中兴5G)\",\n    \"operationType\": 1,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试参数校验：设备名称和设备IP都为空的情况"}}, {"name": "承载故障根因分析-无专业过滤", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceName\": \"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\n    \"deviceIp\": \"*************\",\n    \"startTime\": \"2024-09-27 10:35:50\",\n    \"operationType\": 1,\n    \"tgName\": \"HN_TG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/hn/carrierFaultAnalysis", "host": ["{{baseUrl}}"], "path": ["api", "hn", "carrierFaultAnalysis"]}, "description": "测试不传入专业参数的场景，应该返回全量告警"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "description": "API服务器基础URL"}]}