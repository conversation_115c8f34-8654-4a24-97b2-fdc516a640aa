package com.ffcs.oss.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 告警名称配置类，从nacos读取配置
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "alarm")
@Data
public class AlarmNameConfig {
    /**
     * 专业对应的告警名称配置
     * 格式：specialty -> List<alarmName>
     */
    private Map<String, List<String>> names;

    /**
     * 传输专业告警配置
     */
    private TransmissionAlarmConfig transmission;
    private DataConfig dataConfig;

    @Data
    public static class TransmissionAlarmConfig {
        /**
         * 光缆中断类型的告警名称列表
         */
        private List<String> cableInterruption;

        /**
         * 板卡故障类型的告警名称列表
         */
        private List<String> boardFault;
    }

    @Data
    public static class DataConfig {
        /**
         * 光缆中断类型的告警名称列表
         */
        private List<String> cableInterruption;

        /**
         * 板卡故障类型的告警名称列表
         */
        private List<String> boardFault;
    }
}
