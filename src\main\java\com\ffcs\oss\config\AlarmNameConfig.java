package com.ffcs.oss.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警名称配置类，从nacos读取配置
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "alarm")
@Data
public class AlarmNameConfig {
    /**
     * 专业对应的告警名称配置
     * 格式：specialty -> List<alarmName>
     */
    private Map<String, List<String>> names;

    /**
     * 传输专业告警配置
     */
    private TransmissionAlarmConfig transmission;
    private DataConfig dataConfig;

    /**
     * 故障类型映射配置
     * 格式：faultTypeKey -> displayName
     * 例如：
     * faultTypeMapping:
     *   cableInterruption: "光缆中断"
     *   boardFault: "板卡故障"
     *   powerFault: "停电"
     *   customFault: "自定义故障"
     */
    private Map<String, String> faultTypeMapping;

    @Data
    public static class TransmissionAlarmConfig {
        /**
         * 光缆中断类型的告警名称列表（向下兼容）
         */
        private List<String> cableInterruption;

        /**
         * 板卡故障类型的告警名称列表（向下兼容）
         */
        private List<String> boardFault;

        /**
         * 动态故障类型配置
         * 格式：faultType -> List<alarmName>
         * 例如：
         * faultTypes:
         *   cableInterruption: ["光缆中断", "链路故障"]
         *   boardFault: ["板卡故障", "硬件故障"]
         *   powerFault: ["停电", "断电"]
         *   temperatureFault: ["高温告警", "温度异常"]
         */
        private Map<String, List<String>> faultTypes;

        /**
         * 故障类型优先级配置（数字越小优先级越高）
         * 格式：faultType -> priority
         * 例如：
         * priorities:
         *   cableInterruption: 1
         *   boardFault: 2
         *   powerFault: 3
         */
        private Map<String, Integer> priorities;
    }

    @Data
    public static class DataConfig {
        /**
         * 光缆中断类型的告警名称列表（向下兼容）
         */
        private List<String> cableInterruption;

        /**
         * 板卡故障类型的告警名称列表（向下兼容）
         */
        private List<String> boardFault;

        /**
         * 动态故障类型配置
         * 格式：faultType -> List<alarmName>
         */
        private Map<String, List<String>> faultTypes;

        /**
         * 故障类型优先级配置（数字越小优先级越高）
         * 格式：faultType -> priority
         */
        private Map<String, Integer> priorities;
    }

    /**
     * 获取传输专业所有故障类型的告警名称（包含动态配置和固定配置）
     */
    public Map<String, List<String>> getAllTransmissionFaultTypes() {
        Map<String, List<String>> allFaultTypes = new HashMap<>();

        if (transmission != null) {
            // 添加动态配置的故障类型
            if (transmission.getFaultTypes() != null) {
                allFaultTypes.putAll(transmission.getFaultTypes());
            }

            // 添加固定配置的故障类型（向下兼容）
            if (transmission.getCableInterruption() != null && !transmission.getCableInterruption().isEmpty()) {
                allFaultTypes.put("cableInterruption", transmission.getCableInterruption());
            }
            if (transmission.getBoardFault() != null && !transmission.getBoardFault().isEmpty()) {
                allFaultTypes.put("boardFault", transmission.getBoardFault());
            }
        }

        return allFaultTypes;
    }

    /**
     * 获取数据专业所有故障类型的告警名称（包含动态配置和固定配置）
     */
    public Map<String, List<String>> getAllDataFaultTypes() {
        Map<String, List<String>> allFaultTypes = new HashMap<>();

        if (dataConfig != null) {
            // 添加动态配置的故障类型
            if (dataConfig.getFaultTypes() != null) {
                allFaultTypes.putAll(dataConfig.getFaultTypes());
            }

            // 添加固定配置的故障类型（向下兼容）
            if (dataConfig.getCableInterruption() != null && !dataConfig.getCableInterruption().isEmpty()) {
                allFaultTypes.put("cableInterruption", dataConfig.getCableInterruption());
            }
            if (dataConfig.getBoardFault() != null && !dataConfig.getBoardFault().isEmpty()) {
                allFaultTypes.put("boardFault", dataConfig.getBoardFault());
            }
        }

        return allFaultTypes;
    }

    /**
     * 获取传输专业故障类型优先级
     */
    public Map<String, Integer> getTransmissionPriorities() {
        if (transmission != null && transmission.getPriorities() != null) {
            return transmission.getPriorities();
        }

        // 默认优先级（向下兼容）
        Map<String, Integer> defaultPriorities = new HashMap<>();
        defaultPriorities.put("cableInterruption", 1);
        defaultPriorities.put("boardFault", 2);
        return defaultPriorities;
    }

    /**
     * 获取数据专业故障类型优先级
     */
    public Map<String, Integer> getDataPriorities() {
        if (dataConfig != null && dataConfig.getPriorities() != null) {
            return dataConfig.getPriorities();
        }

        // 默认优先级（向下兼容）
        Map<String, Integer> defaultPriorities = new HashMap<>();
        defaultPriorities.put("cableInterruption", 1);
        defaultPriorities.put("boardFault", 2);
        return defaultPriorities;
    }

    /**
     * 获取故障类型映射配置
     * @return 故障类型映射Map，key为故障类型标识，value为显示名称
     */
    public Map<String, String> getFaultTypeMapping() {
        if (faultTypeMapping != null && !faultTypeMapping.isEmpty()) {
            return faultTypeMapping;
        }

        // 返回默认映射（向下兼容）
        Map<String, String> defaultMapping = new HashMap<>();
        defaultMapping.put("cableInterruption", CommonConstant.FAULT_NAME_CABLE_INTERRUPTION);
        defaultMapping.put("boardFault", CommonConstant.FAULT_NAME_BOARD_FAULT);
        defaultMapping.put("powerFault", CommonConstant.FAULT_NAME_POWER_OUTAGE);
        defaultMapping.put("temperatureFault", CommonConstant.FAULT_NAME_ENVIRONMENT_FAULT);
        defaultMapping.put("networkFault", CommonConstant.FAULT_NAME_NETWORK_FAULT);
        defaultMapping.put("hardwareFault", CommonConstant.FAULT_NAME_HARDWARE_FAULT);
        defaultMapping.put("softwareFault", CommonConstant.FAULT_NAME_SOFTWARE_FAULT);
        defaultMapping.put("configurationFault", CommonConstant.FAULT_NAME_CONFIGURATION_FAULT);
        defaultMapping.put("performanceFault", CommonConstant.FAULT_NAME_PERFORMANCE_FAULT);
        defaultMapping.put("securityFault", CommonConstant.FAULT_NAME_SECURITY_FAULT);
        defaultMapping.put("communicationFault", CommonConstant.FAULT_NAME_COMMUNICATION_FAULT);
        defaultMapping.put("protocolFault", CommonConstant.FAULT_NAME_PROTOCOL_FAULT);
        return defaultMapping;
    }
}
