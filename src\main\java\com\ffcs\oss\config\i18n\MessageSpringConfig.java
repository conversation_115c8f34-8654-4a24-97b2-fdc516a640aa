package com.ffcs.oss.config.i18n;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.util.ResourceUtils;
import org.springframework.web.servlet.LocaleResolver;

import java.io.File;
import java.io.FileNotFoundException;


/**
 * @ClassName SpringConfig
 * @Description Spring配置
 * <AUTHOR>
 * @Date 2022/8/23 11:30
 */
@Slf4j
@Configuration
public class MessageSpringConfig {

    /**
     * 应用名称
     */
    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private MessageConfig messageConfig;

    @Bean
    public LocaleResolver localeResolver() {
        return new MessageLocaleResolver();
    }

    @Primary
    @Bean(name = "messageSource")
    @DependsOn(value = "messageConfig")
    public ReloadableResourceBundleMessageSource messageSource() throws FileNotFoundException {
        String path = null;
        if (!messageConfig.getLocalfile()) {
            String baseName = StringUtils.isNotBlank(messageConfig.getBasename()) ? messageConfig.getBasename() : "basename";
            path = ResourceUtils.FILE_URL_PREFIX + ResourceUtils.getURL("classpath:").getPath() + File.separator + File.separator + baseName;
            log.info("Nacos国际化配置本地缓存路径:{}", path);
        } else {
            path = messageConfig.getBasename();
            if (!path.contains("classpath")) {
                path = "classpath:" + path;
            }
            log.info("国际化配置本地路径:{}", path);
        }
        if (StringUtils.isBlank(path)) {
            path = "classpath:i18n/messages";
            log.info("国际化未配置，使用默认路径:{}", path);
        }
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename(path);
        messageSource.setDefaultEncoding(messageConfig.getEncoding());
        messageSource.setCacheMillis(messageConfig.getCacheMillis());
        return messageSource;
    }


}
