package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("返回内容")
public class ServiceResp<T> implements Serializable {
    private static final long serialVersionUID = 3713138005539550112L;
    @ApiModelProperty("响应头")
    private RespHeader head = new RespHeader();
    @ApiModelProperty("响应内容")
    private T body;

    protected ServiceResp() {
    }

    public static ServiceResp getInstance() {
        return new ServiceResp();
    }

    public static <T> ServiceResp<T> getInstance(Class<T> responseType) {
        return new ServiceResp();
    }

    public ServiceResp error() {
        this.head.setRespCode(-1);
        this.head.setRespMsg("operate fail");
        this.setBody((T) null);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public ServiceResp error(String respMsg) {
        this.head.setRespCode(-1);
        this.head.setRespMsg(respMsg);
        this.setBody((T) null);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public ServiceResp error(int respCode, String respMsg) {
        this.head.setRespCode(respCode);
        this.head.setRespMsg(respMsg);
        this.setBody((T) null);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public ServiceResp success(String respMsg) {
        this.head.setRespCode(0);
        this.head.setRespMsg(respMsg);
        this.setBody((T) null);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public ServiceResp<T> success(T body, String respMsg) {
        this.head.setRespCode(0);
        this.head.setRespMsg(respMsg);
        this.setBody(body);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public ServiceResp<T> success(T body) {
        this.head.setRespCode(0);
        this.head.setRespMsg("operate success");
        this.setBody(body);
        this.head.setResTime(String.valueOf(System.currentTimeMillis()));
        return this;
    }

    public boolean isSuccess() {
        return this.getHead().getRespCode() == 0;
    }

    public boolean hasRecord() {
        return this.isSuccess() && this.body != null;
    }

    public RespHeader getHead() {
        return this.head;
    }

    public void setHead(RespHeader head) {
        this.head = head;
    }

    public T getBody() {
        return this.body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    @Override
    public String toString() {
        return "ServiceResp{head=" + this.head + ", body=" + this.body + '}';
    }
}
