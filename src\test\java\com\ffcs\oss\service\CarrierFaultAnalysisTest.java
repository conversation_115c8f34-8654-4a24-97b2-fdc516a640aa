package com.ffcs.oss.service;

import com.ffcs.oss.domain.ApiCarrierQuery;
import com.ffcs.oss.domain.ServiceResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 承载故障根因分析功能测试类
 */
@SpringBootTest
public class CarrierFaultAnalysisTest {

    @Autowired
    private IHnApiService hnApiService;

    @Test
    public void testCarrierFaultAnalysisWithIp() throws Exception {
        // 测试使用设备IP查询
        ApiCarrierQuery query = new ApiCarrierQuery();
        query.setDeviceIp("*************");
        query.setDeviceName("NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维");
        query.setStartTime("2024-09-27 10:35:50");
        query.setSpecialty("无线(中兴5G)");
        query.setOperationType(1);
        query.setTgName("HN_TG");

        ServiceResp result = hnApiService.carrierFaultAnalysis(query);
        System.out.println("测试结果（使用IP）: " + result);
    }

    @Test
    public void testCarrierFaultAnalysisWithDeviceName() throws Exception {
        // 测试使用设备名称查询
        ApiCarrierQuery query = new ApiCarrierQuery();
        query.setDeviceName("NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维");
        query.setStartTime("2024-09-27 10:35:50");
        query.setSpecialty("无线(中兴5G)");
        query.setOperationType(0);
        query.setTgName("HN_TG");

        ServiceResp result = hnApiService.carrierFaultAnalysis(query);
        System.out.println("测试结果（使用设备名称）: " + result);
    }

    @Test
    public void testCarrierFaultAnalysisWithoutStationInfo() throws Exception {
        // 测试无局站信息的情况
        ApiCarrierQuery query = new ApiCarrierQuery();
        query.setDeviceIp("192.168.1.999");  // 不存在的IP
        query.setDeviceName("不存在的设备");
        query.setStartTime("2024-09-27 10:35:50");
        query.setSpecialty("无线(中兴5G)");
        query.setOperationType(1);
        query.setTgName("HN_TG");

        ServiceResp result = hnApiService.carrierFaultAnalysis(query);
        System.out.println("测试结果（无局站信息）: " + result);
    }

    @Test
    public void testCarrierFaultAnalysisParameterValidation() throws Exception {
        // 测试参数校验
        ApiCarrierQuery query = new ApiCarrierQuery();
        // 不设置设备IP和设备名称
        query.setStartTime("2024-09-27 10:35:50");
        query.setSpecialty("无线(中兴5G)");
        query.setOperationType(1);
        query.setTgName("HN_TG");

        ServiceResp result = hnApiService.carrierFaultAnalysis(query);
        System.out.println("测试结果（参数校验）: " + result);
    }
}
