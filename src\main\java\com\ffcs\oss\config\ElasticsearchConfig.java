package com.ffcs.oss.config;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "elasticsearch.server")
public class ElasticsearchConfig {
    private String url;
    private String tcpaddr;
    private String httpaddr;
    private String protocol;
    private String username;
    private String password;
    private String clustername;
    private Integer size;
    @Value("${elasticsearch.server.username:}")
    private String user;

    @Override
    public String toString() {
        return "ElasticsearchConfig{" +
                "url='" + url + '\'' +
                ", tcpaddr='" + tcpaddr + '\'' +
                ", httpaddr='" + httpaddr + '\'' +
                ", protocol='" + protocol + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", clustername='" + clustername + '\'' +
                ", size=" + size +
                ", user='" + user + '\'' +
                '}';
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUser() {
        return user;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTcpaddr() {
        return tcpaddr;
    }

    public void setTcpaddr(String tcpaddr) {
        this.tcpaddr = tcpaddr;
    }

    public String getHttpaddr() {
        return httpaddr;
    }

    public void setHttpaddr(String httpaddr) {
        this.httpaddr = httpaddr;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getClustername() {
        return clustername;
    }

    public void setClustername(String clustername) {
        this.clustername = clustername;
    }

    public String getHttpEsJson() {
        Map<String, Object> esJsonMap = new HashMap<>();
        esJsonMap.put("clusterNodes", httpaddr);
        esJsonMap.put("cluster.name", clustername);
        return JSONObject.toJSONString(esJsonMap);
    }
}
