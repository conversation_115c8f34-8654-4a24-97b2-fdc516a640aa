package com.ffcs.oss.config.i18n;


import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Locale;

/**
 * 自定义国际化解析器
 *
 * <AUTHOR>
 * @date 2022/8/23 11:37
 **/
public class MessageLocaleResolver implements LocaleResolver {

    private static final String enLocal = "en";
    private static final String enLocalLanguage = "en_US";
    private static final String zhLocal = "zh";
    private static final String zhLocalLanguage = "zh_CN";
    /**
     * 请求header字段
     */
    private static final String LANG = "lang";

    /**
     * session
     */
    private static final String LANG_SESSION = "lang_session";

    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        //强求语言切换
        String lang = request.getHeader(LANG);
        //浏览器Request语言
        if (StringUtils.isBlank(lang)) {
            String resLocal = request.getLocale().getLanguage();
            if (resLocal.startsWith(enLocal)) {
                lang = enLocalLanguage;
            }
            if (resLocal.startsWith(zhLocal)) {
                lang = zhLocalLanguage;
            }
        }
        Locale locale = Locale.getDefault();
        if (StringUtils.isNotBlank(lang)) {
            String[] language = lang.split("_");
            locale = new Locale(language[0], language[1]);
            HttpSession session = request.getSession();
            session.setAttribute(LANG_SESSION, locale);
        } else {
            HttpSession session = request.getSession();
            Locale localeInSession = (Locale) session.getAttribute(LANG_SESSION);
            if (localeInSession != null) {
                locale = localeInSession;
            }
        }
        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Locale locale) {
    }


}
