package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("动环温湿度故障根因查询条件")
public class ApiDhQuery {
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String neName;

    @ApiModelProperty(value = "局站编码")
    private String stationCode;
}
