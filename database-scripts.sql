-- PostgreSQL数据库脚本
-- 承载故障根因分析功能相关表结构

-- 1. 确保kg.cm_device表存在（设备信息表）
-- 此表用于根据设备名称查询基站站址信息
CREATE TABLE IF NOT EXISTS kg.cm_device (
    id SERIAL PRIMARY KEY,
    nameinemes VARCHAR(500) NOT NULL COMMENT '设备名称',
    cell_site VARCHAR(200) COMMENT '基站站址',
    device_ip VARCHAR(50) COMMENT '设备IP地址',
    station_code VARCHAR(100) COMMENT '局站编码',
    station_name VARCHAR(200) COMMENT '局站名称',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为设备名称创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_cm_device_nameinemes ON kg.cm_device(nameinemes);
CREATE INDEX IF NOT EXISTS idx_cm_device_ip ON kg.cm_device(device_ip);
CREATE INDEX IF NOT EXISTS idx_cm_device_station_code ON kg.cm_device(station_code);

-- 2. 确保rm_area表存在（区域信息表）
-- 此表用于根据局站编码查询局站名称
CREATE TABLE IF NOT EXISTS rm_area (
    id SERIAL PRIMARY KEY,
    alias VARCHAR(100) NOT NULL COMMENT '局站编码/别名',
    name VARCHAR(200) NOT NULL COMMENT '局站名称',
    area_type VARCHAR(50) COMMENT '区域类型',
    parent_id INTEGER COMMENT '父级区域ID',
    level_num INTEGER COMMENT '层级',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为局站编码创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_rm_area_alias ON rm_area(alias);
CREATE INDEX IF NOT EXISTS idx_rm_area_name ON rm_area(name);

-- 3. 可选：创建设备IP与局站映射表（如果需要本地缓存图谱查询结果）
CREATE TABLE IF NOT EXISTS kg.device_station_mapping (
    id SERIAL PRIMARY KEY,
    device_ip VARCHAR(50) NOT NULL COMMENT '设备IP',
    device_name VARCHAR(500) COMMENT '设备名称',
    station_code VARCHAR(100) NOT NULL COMMENT '局站编码',
    station_name VARCHAR(200) COMMENT '局站名称',
    station_id VARCHAR(100) COMMENT '局站ID',
    source_type VARCHAR(20) DEFAULT 'GRAPH' COMMENT '数据来源：GRAPH-图谱查询，MANUAL-手工录入',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为设备IP和局站编码创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_device_station_unique ON kg.device_station_mapping(device_ip, station_code) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_device_station_ip ON kg.device_station_mapping(device_ip);
CREATE INDEX IF NOT EXISTS idx_device_station_name ON kg.device_station_mapping(device_name);

-- 4. 可选：创建告警配置表（如果需要在数据库中管理告警名称配置而不是使用nacos）
CREATE TABLE IF NOT EXISTS kg.alarm_name_config (
    id SERIAL PRIMARY KEY,
    specialty VARCHAR(100) NOT NULL COMMENT '专业名称',
    alarm_name VARCHAR(200) NOT NULL COMMENT '告警名称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为专业和告警名称创建索引
CREATE INDEX IF NOT EXISTS idx_alarm_config_specialty ON kg.alarm_name_config(specialty) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_alarm_config_name ON kg.alarm_name_config(alarm_name) WHERE is_active = TRUE;

-- 5. 添加表注释
COMMENT ON TABLE kg.cm_device IS '设备信息表';
COMMENT ON TABLE rm_area IS '区域信息表';
COMMENT ON TABLE kg.device_station_mapping IS '设备与局站映射表';
COMMENT ON TABLE kg.alarm_name_config IS '告警名称配置表';

-- 6. 示例数据插入（可选）
-- 插入一些测试数据
INSERT INTO kg.cm_device (nameinemes, cell_site, device_ip, station_code, station_name) VALUES
('NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维', '三亚蜈支洲岛', '*************', 'QHA.DFJ', '东风')
ON CONFLICT DO NOTHING;

INSERT INTO rm_area (alias, name, area_type) VALUES
('QHA.DFJ', '东风', '局站'),
('QHA.SY', '三亚', '局站'),
('QHA.HK', '海口', '局站')
ON CONFLICT DO NOTHING;

-- 插入告警名称配置示例数据
INSERT INTO kg.alarm_name_config (specialty, alarm_name) VALUES
('无线(中兴5G)', 'DU小区退服'),
('无线(中兴5G)', 'RRU断链'),
('无线(中兴5G)', '基站掉电'),
('传输', '光缆中断'),
('传输', '设备断电'),
('动环', '停电'),
('动环', '欠压'),
('动环', '高温')
ON CONFLICT DO NOTHING;
