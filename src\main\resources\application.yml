server:
  port: 9100

spring:
  cache:
    default-ttl: 1200
    type: none
  application:
    # 应用名称
    name: hn-api-nacos
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  messages:
    #true使用本地包文件，false使用nacos配置
    localfile: true
    basename: i18n/messages
    encoding: UTF-8
    cacheMillis: 10000
  management:
    metrics:
      enable: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ******************************************************************************=${spring.application.name}
    username: pg5gc_api_dev
    password: ENC(HvWjEY0JBB1ZGAe8SzOQGSjwyJO1KNvu)


management:
  metrics:
    enable: false

logging:
  level:
    root: info

# 接口地址配置
hnconfig:
  if-test: Y
  query-nename-url: http://localhost:9100/api/test/get1
  query-csline-url: http://localhost:9100/api/test/get2
  query-dhwd-url: http://localhost:9100/api/test/get3
  query-station-by-ip-url: http://localhost:9100/api/test/get4
  query-alarm-url: http://localhost:9100/api/test/get5
  query-peer-device-url: http://localhost:9100/api/test/get6
#  query-peer-device-url: http://localhost:9100/api/test/get6

# Elasticsearch配置
elasticsearch:
  index:
    # 获取告警数据的索引
    alarmDataIndex: /alarm_data_index/_search
  server:
    tcpaddr: **************:9301,**************:9301,**************:9301
    httpaddr: **************:9201,**************:9201,**************:9201
    clustername: oss-test-es7
    protocol: http
    username: elastic
    # password: 'Ffcsoss!@#1115'
    password: 'ENC(gmHp61HVVUwqJba7CYPJ1jKg+s3XdbNa)'
    version: 7.x
#    url: http://**************:9211
alarm:
  names:
    # 无线专业的告警名称配置
    "station":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"

  # 故障类型映射配置（全局配置，支持自定义故障类型显示名称）
  faultTypeMapping:
    # 基础故障类型
    cableInterruption: "光缆中断"
    boardFault: "板卡故障"
    powerFault: "停电"
    temperatureFault: "动环故障"
    # 网络相关故障类型
    networkFault: "网络故障"
    communicationFault: "通信故障"
    protocolFault: "协议故障"
    # 设备相关故障类型
    hardwareFault: "硬件故障"
    softwareFault: "软件故障"
    configurationFault: "配置故障"
    performanceFault: "性能故障"
    securityFault: "安全故障"
    # 自定义故障类型示例
    databaseFault: "数据库故障"
    storageFault: "存储故障"
    middlewareFault: "中间件故障"
    applicationFault: "应用故障"

  # 传输专业告警配置（独立配置，与names区分）
  transmission:
    # 光缆中断类型的告警名称列表（向下兼容）
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表（向下兼容）
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"

    # 动态故障类型配置（推荐使用）
    faultTypes:
      # 光缆中断类型
      cableInterruption:
        - "光纤中断告警"
        - "传输链路故障"
        - "光缆断纤"
        - "光缆中断"
        - "链路中断"
      # 板卡故障类型
      boardFault:
        - "板卡硬件故障"
        - "设备异常告警"
        - "板卡故障"
        - "硬件故障"
        - "设备故障"
      # 网络故障类型
      networkFault:
        - "网络连接异常"
        - "路由故障"
        - "交换机故障"
        - "网络拥塞"
      # 电源故障类型
      powerFault:
        - "停电告警"
        - "电源故障"
        - "UPS故障"
        - "电压异常"
      # 温度故障类型
      temperatureFault:
        - "高温告警"
        - "温度异常"
        - "散热故障"
        - "环境温度超标"

    # 故障类型优先级配置（数字越小优先级越高）
    priorities:
      cableInterruption: 1    # 光缆中断优先级最高
      powerFault: 2           # 电源故障次之
      temperatureFault: 3     # 温度故障
      networkFault: 4         # 网络故障
      boardFault: 5           # 板卡故障优先级最低
  data-config:
    # 光缆中断类型的告警名称列表（向下兼容）
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表（向下兼容）
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"

    # 动态故障类型配置（推荐使用）
    faultTypes:
      # 光缆中断类型
      cableInterruption:
        - "光纤中断告警"
        - "传输链路故障"
        - "光缆断纤"
        - "光缆中断"
        - "链路中断"
      # 板卡故障类型
      boardFault:
        - "板卡硬件故障"
        - "设备异常告警"
        - "板卡故障"
        - "硬件故障"
        - "设备故障"
      # 软件故障类型
      softwareFault:
        - "软件异常"
        - "程序崩溃"
        - "服务停止"
        - "应用故障"
      # 配置故障类型
      configurationFault:
        - "配置错误"
        - "参数异常"
        - "配置文件损坏"
        - "设置不当"
      # 性能故障类型
      performanceFault:
        - "性能下降"
        - "响应超时"
        - "处理能力不足"
        - "资源耗尽"

    # 故障类型优先级配置（数字越小优先级越高）
    priorities:
      cableInterruption: 1      # 光缆中断优先级最高
      configurationFault: 2     # 配置故障次之
      softwareFault: 3          # 软件故障
      performanceFault: 4       # 性能故障
      boardFault: 5             # 板卡故障优先级最低