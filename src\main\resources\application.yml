server:
  port: 9100

spring:
  cache:
    default-ttl: 1200
    type: none
  application:
    # 应用名称
    name: hn-api-nacos
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  messages:
    #true使用本地包文件，false使用nacos配置
    localfile: true
    basename: i18n/messages
    encoding: UTF-8
    cacheMillis: 10000
  management:
    metrics:
      enable: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://*************:15432/db_oss3?currentSchema=kg&ApplicationName=${spring.application.name}
    username: pg5gc_api_dev
    password: ENC(HvWjEY0JBB1ZGAe8SzOQGSjwyJO1KNvu)


management:
  metrics:
    enable: false

logging:
  level:
    root: info

# 接口地址配置
hnconfig:
  if-test: N
  query-nename-url: http://localhost:9100/api/test/get1
  query-csline-url: http://localhost:9100/api/test/get2
  query-dhwd-url: http://localhost:9100/api/test/get3
  query-station-by-ip-url: http://localhost:9100/api/test/get4
  query-alarm-url: http://localhost:9100/api/test/get5
  query-peer-device-url: http://localhost:9100/api/test/get6
#  query-peer-device-url: http://localhost:9100/api/test/get6

# Elasticsearch配置
elasticsearch:
  index:
    # 获取告警数据的索引
    alarmDataIndex: /alarm_data_index/_search
  server:
    tcpaddr: **************:9301,**************:9301,**************:9301
    httpaddr: **************:9201,**************:9201,**************:9201
    clustername: oss-test-es7
    protocol: http
    username: elastic
    # password: 'Ffcsoss!@#1115'
    password: 'ENC(gmHp61HVVUwqJba7CYPJ1jKg+s3XdbNa)'
    version: 7.x
#    url: http://**************:9211
alarm:
  names:
    # 无线专业的告警名称配置
    "station":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"

  # 传输专业告警配置（独立配置，与names区分）
  transmission:
    # 光缆中断类型的告警名称列表
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"
  data-config:
    # 光缆中断类型的告警名称列表
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"