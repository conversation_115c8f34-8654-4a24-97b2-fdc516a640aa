package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("故障根因查询条件")
public class ApiCgyQuery {
    /**
     * 类型：1传输；2数据
     */
    @ApiModelProperty(value = "故障场景")
    private String fultName;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "告警名称")
    private String alrmName;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "告警ID")
    private String alarmId;
}
