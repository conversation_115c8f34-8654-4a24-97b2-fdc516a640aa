门户权限和获取登录用户信息包使用方法
1.引入jar包

       <dependency>
            <groupId>com.ffcs.oss</groupId>
            <artifactId>portal-security-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>

2. 需要获取登录用户信息： String username = PtSecurityUtils.getUsername()；
   //SecurityUtils.getCurrentUserLogin().get()替换PtSecurityUtils.getUsername()
   判断是否管理员：Boolean isAdmin = PtSecurityUtils.isAdmin();
   //SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) 替换PtSecurityUtils.isAdmin()

3.配置信息：
oss:
 permission:
 enabled: true  #默认是true
 request-url: http://cs-sm-qry-srv/rsc/permission/hasPermissionOrNot #默认地址

4.国际化配置：
  （1）本地配置：参考springboot配置
  （2）Nacos配置中心配置方式：
      1、设置：     
       spring: 
            messages:
               localfile: false   #表示不使用本地配置
               basename:  i18n/messages  #最后的一段就是配置nacos文件的前缀，文件是properties文件
      2、配置中心配置：
          <1>默认只需配置nacos中心，跟普通配置信息放同一namespace和group;
          <2>国际化独立一namespace和group，独立配置：
           spring:
              cloud:
                 nacos:  
                   config: 
                      # 国际化配置命名空间(不填默认public)
                      message-nmespace: 6bddda45-ab58-4ebf-87a3-cbb4c7e9a3b0
                      # 国际化配置分组(不填默认DEFAULT_GROUP)
                      message-group: DEFAULT_GROUP
