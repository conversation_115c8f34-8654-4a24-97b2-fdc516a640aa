# Nacos配置示例 - 告警名称配置
# 配置文件名：alarm-config.yml
# 在nacos中创建此配置文件，用于配置不同专业对应的告警名称

alarm:
  names:
    # 无线专业的告警名称配置
    "无线(中兴5G)":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"
    

    
    # 动环专业的告警名称配置
    "动环":
      - "停电"
      - "欠压"
      - "高温"
      - "湿度异常"
    
    # 核心网专业的告警名称配置
    "核心网":
      - "服务器故障"
      - "网络中断"
      - "数据库异常"
      - "应用服务异常"

  # 传输专业告警配置（独立配置，与names区分）
  transmission:
    # 光缆中断类型的告警名称列表
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"

# 注意：
# 1. 专业名称需要与实际传入的specialty参数完全匹配
# 2. 告警名称支持模糊匹配，只要告警标题包含配置的名称即可
# 3. 如果某个专业未配置或配置为空，则该专业的告警不会被过滤
# 4. 如果整个配置为空或specialty参数为空，则不进行过滤，返回全量告警
# 5. 对于传输专业：
#    - transmission.cableInterruption中配置的告警名称对应故障类型：光缆中断
#    - transmission.boardFault中配置的告警名称对应故障类型：板卡故障
#    - 如果同时匹配到光缆中断和板卡故障类型告警，优先返回：光缆中断
#    - 传输专业的告警配置独立于names配置，避免冲突
