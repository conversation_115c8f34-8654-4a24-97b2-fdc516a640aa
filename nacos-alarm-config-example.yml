# Nacos配置示例 - 告警名称配置
# 配置文件名：alarm-config.yml
# 在nacos中创建此配置文件，用于配置不同专业对应的告警名称

alarm:
  names:
    # 无线专业的告警名称配置
    "无线(中兴5G)":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"
    

    
    # 动环专业的告警名称配置
    "动环":
      - "停电"
      - "欠压"
      - "高温"
      - "湿度异常"
    
    # 核心网专业的告警名称配置
    "核心网":
      - "服务器故障"
      - "网络中断"
      - "数据库异常"
      - "应用服务异常"

  # 传输专业告警配置（独立配置，与names区分）
  transmission:
    # 光缆中断类型的告警名称列表（向下兼容）
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表（向下兼容）
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"

    # 动态故障类型配置（推荐使用，支持任意故障类型）
    faultTypes:
      # 光缆中断类型
      cableInterruption:
        - "光纤中断告警"
        - "传输链路故障"
        - "光缆断纤"
        - "光缆中断"
        - "链路中断"
      # 板卡故障类型
      boardFault:
        - "板卡硬件故障"
        - "设备异常告警"
        - "板卡故障"
        - "硬件故障"
        - "设备故障"
      # 网络故障类型（新增）
      networkFault:
        - "网络连接异常"
        - "路由故障"
        - "交换机故障"
        - "网络拥塞"
        - "带宽不足"
      # 电源故障类型（新增）
      powerFault:
        - "停电告警"
        - "电源故障"
        - "UPS故障"
        - "电压异常"
        - "电池故障"
      # 温度故障类型（新增）
      temperatureFault:
        - "高温告警"
        - "温度异常"
        - "散热故障"
        - "环境温度超标"
        - "冷却系统故障"
      # 通信故障类型（新增）
      communicationFault:
        - "通信中断"
        - "信号丢失"
        - "协议错误"
        - "数据传输异常"
      # 安全故障类型（新增）
      securityFault:
        - "安全威胁"
        - "入侵检测"
        - "认证失败"
        - "权限异常"

    # 故障类型优先级配置（数字越小优先级越高）
    priorities:
      powerFault: 1             # 电源故障优先级最高
      cableInterruption: 2      # 光缆中断次之
      temperatureFault: 3       # 温度故障
      securityFault: 4          # 安全故障
      networkFault: 5           # 网络故障
      communicationFault: 6     # 通信故障
      boardFault: 7             # 板卡故障优先级最低

  # 数据专业告警配置
  data-config:
    # 光缆中断类型的告警名称列表（向下兼容）
    cableInterruption:
      - "光纤中断告警"
      - "传输链路故障"
      - "光缆断纤"
      - "光缆中断"
      - "链路中断"

    # 板卡故障类型的告警名称列表（向下兼容）
    boardFault:
      - "板卡硬件故障"
      - "设备异常告警"
      - "板卡故障"
      - "硬件故障"
      - "设备故障"

    # 动态故障类型配置（推荐使用，支持任意故障类型）
    faultTypes:
      # 光缆中断类型
      cableInterruption:
        - "光纤中断告警"
        - "传输链路故障"
        - "光缆断纤"
        - "光缆中断"
        - "链路中断"
      # 板卡故障类型
      boardFault:
        - "板卡硬件故障"
        - "设备异常告警"
        - "板卡故障"
        - "硬件故障"
        - "设备故障"
      # 软件故障类型（新增）
      softwareFault:
        - "软件异常"
        - "程序崩溃"
        - "服务停止"
        - "应用故障"
        - "系统错误"
      # 配置故障类型（新增）
      configurationFault:
        - "配置错误"
        - "参数异常"
        - "配置文件损坏"
        - "设置不当"
        - "配置冲突"
      # 性能故障类型（新增）
      performanceFault:
        - "性能下降"
        - "响应超时"
        - "处理能力不足"
        - "资源耗尽"
        - "内存不足"
      # 协议故障类型（新增）
      protocolFault:
        - "协议错误"
        - "协议不匹配"
        - "协议超时"
        - "协议解析失败"

    # 故障类型优先级配置（数字越小优先级越高）
    priorities:
      cableInterruption: 1      # 光缆中断优先级最高
      configurationFault: 2     # 配置故障次之
      protocolFault: 3          # 协议故障
      softwareFault: 4          # 软件故障
      performanceFault: 5       # 性能故障
      boardFault: 6             # 板卡故障优先级最低

# 注意：
# 1. 专业名称需要与实际传入的specialty参数完全匹配
# 2. 告警名称支持模糊匹配，只要告警标题包含配置的名称即可
# 3. 如果某个专业未配置或配置为空，则该专业的告警不会被过滤
# 4. 如果整个配置为空或specialty参数为空，则不进行过滤，返回全量告警
# 5. 对于传输专业：
#    - transmission.cableInterruption中配置的告警名称对应故障类型：光缆中断
#    - transmission.boardFault中配置的告警名称对应故障类型：板卡故障
#    - 如果同时匹配到光缆中断和板卡故障类型告警，优先返回：光缆中断
#    - 传输专业的告警配置独立于names配置，避免冲突
