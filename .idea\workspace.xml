<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="466932b6-c822-4708-b0a5-f2278d358880" name="Changes" comment="【新增】查询传输、数据设备告警">
      <change afterPath="$PROJECT_DIR$/api-logs/sys-error.2025-07-30.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/api-logs/sys-info.2025-07-30.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/database-scripts.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/nacos-alarm-config-example.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/ffcs/oss/config/DynamicAlarmConfigTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/动态故障类型配置功能说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/承载故障根因分析功能说明.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/postman-collection.json" beforeDir="false" afterPath="$PROJECT_DIR$/postman-collection.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/config/AlarmNameConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/config/AlarmNameConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/config/CommonConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/config/CommonConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:/Program Files/Java/sx_maven/apache-maven-3.6.0" />
        <option name="userSettingsFile" value="D:\Program Files\Java\sx_maven\apache-maven-3.6.0\conf\settings_ffcs.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30aMVtmbexNA0TiED4ciFHD4jqb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ApiPost:METDOD_SEND_RECORD:hn-api-nacos": "{\"/api/fault/carrierFaultAnalysis\":[{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"传输\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754273690671\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 02:14:50 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":10.0,\"minute\":14.0,\"second\":50.0,\"nano\":7.198129E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"传输\\\",\\n    \\\"operationType\\\": 0,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754273953273\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓鍋滅數鏁呴殰锛涙晠闅滆瘑鍒负鍔ㄧ幆鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍔ㄧ幆鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 02:19:13 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":10.0,\"minute\":19.0,\"second\":13.0,\"nano\":2.78605E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 0,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754274699275\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓鍋滅數鏁呴殰锛涙晠闅滆瘑鍒负鍔ㄧ幆鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍔ㄧ幆鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 02:31:39 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":10.0,\"minute\":31.0,\"second\":39.0,\"nano\":3.038874E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754274948112\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 02:35:48 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":10.0,\"minute\":35.0,\"second\":48.0,\"nano\":1.227827E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754277032624\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 03:10:32 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":11.0,\"minute\":10.0,\"second\":32.0,\"nano\":6.595321E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n    \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754277601874\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 03:20:01 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":11.0,\"minute\":20.0,\"second\":1.0,\"nano\":9.266864E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754290297843\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:51:37 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":51.0,\"second\":37.0,\"nano\":8.506128E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754290367554\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:52:47 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":52.0,\"second\":47.0,\"nano\":5.59036E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754290404889\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:53:24 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":53.0,\"second\":24.0,\"nano\":8.921969E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754290769651\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:59:29 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":59.0,\"second\":29.0,\"nano\":7.196701E8}}},{\"url\":\"http://localhost:9100/api/fault/carrierFaultAnalysis\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"searchType\\\": 1\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-动环故障，1-停电\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754290869706\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氳澶囦綅浜庝笢椋庯紝鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱鍥犱笢椋庡湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鍋滅數\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 07:01:09 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":1.0,\"second\":9.0,\"nano\":7.484207E8}}}],\"/api/fault/transmissionAlarmQuery\":[{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"光纤中断告警\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754289898917\\\",\\\"ticket\\\":null,\\\"respCode\\\":-1,\\\"respMsg\\\":\\\"鍙傛暟涓嶅畬鏁�\\\"},\\\"body\\\":null,\\\"success\\\":false}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:44:58 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":44.0,\"second\":58.0,\"nano\":9.444178E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"光纤中断告警\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754289912214\\\",\\\"ticket\\\":null,\\\"respCode\\\":-1,\\\"respMsg\\\":\\\"鍙傛暟涓嶅畬鏁�\\\"},\\\"body\\\":null,\\\"success\\\":false}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:45:12 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":45.0,\"second\":12.0,\"nano\":2.166223E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"光纤中断告警\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\"\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754289935975\\\",\\\"ticket\\\":null,\\\"respCode\\\":-1,\\\"respMsg\\\":\\\"鍙傛暟涓嶅畬鏁�\\\"},\\\"body\\\":null,\\\"success\\\":false}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 06:45:35 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":14.0,\"minute\":45.0,\"second\":35.0,\"nano\":9.775054E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\"\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754291651161\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧锛岃璁惧鏁呴殰鍙戠敓鏃舵锛岀浉鍏崇殑浼犺緭璁惧杩愯姝ｅ父鏈彂鐢熸晠闅滐紱涓嬩竴姝ユ帓鏌ユ暟鎹澶�\\\",\\\"fultName\\\":\\\"涓嬩竴姝ユ帓鏌ユ暟鎹澶�\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 07:14:11 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":14.0,\"second\":11.0,\"nano\":2.000965E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:9100/api/fault/transmissionAlarmQuery\\n\\nConnect to localhost:9100 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":20.0,\"second\":3.0,\"nano\":2.234105E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754292212701\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧锛岃璁惧鏁呴殰鍙戠敓鏃舵锛岀浉鍏崇殑浼犺緭璁惧杩愯姝ｅ父鏈彂鐢熸晠闅滐紱涓嬩竴姝ユ帓鏌ユ暟鎹澶�\\\",\\\"fultName\\\":\\\"涓嬩竴姝ユ帓鏌ユ暟鎹澶�\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 07:23:32 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":23.0,\"second\":32.0,\"nano\":7.43097E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754292433764\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧锛岀浉鍏崇殑浼犺緭璁惧鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛涙牴鎹祫婧愭壙杞藉浘璋卞垎鏋愶細鐩稿叧鐨勪紶杈撹澶囷紝鐩稿叧鐨勪紶杈撹澶囧湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"浼犺緭涓撲笟鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 07:27:13 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":27.0,\"second\":13.0,\"nano\":7.985781E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754293980907\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧锛岀浉鍏崇殑浼犺緭璁惧鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛涙牴鎹祫婧愭壙杞藉浘璋卞垎鏋愶細鐩稿叧鐨勪紶杈撹澶囷紝鐩稿叧鐨勪紶杈撹澶囧湪2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"浼犺緭涓撲笟鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 07:53:00 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":15.0,\"minute\":53.0,\"second\":0.0,\"nano\":9.46523E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754294471271\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧*************锛岀浉鍏崇殑浼犺緭璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛涙牴鎹祫婧愭壙杞藉浘璋卞垎鏋愶細鐩稿叧鐨勪紶杈撹澶�*************锛岀浉鍏崇殑浼犺緭璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"浼犺緭涓撲笟鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 08:01:11 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":16.0,\"minute\":1.0,\"second\":11.0,\"nano\":2.772624E8}}},{\"url\":\"http://localhost:9100/api/fault/transmissionAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"deviceName\\\": \\\"\\\",\\n    \\\"deviceIp\\\": \\\"*************\\\",\\n    \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n    \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n    \\\"operationType\\\": 1,\\n    \\\"searchType\\\": 1,\\n    \\\"deviceType\\\": \\\"\\\",\\n    \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754297774175\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑浼犺緭璁惧*************锛岀浉鍏崇殑浼犺緭璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛涙牴鎹祫婧愭壙杞藉浘璋卞垎鏋愶細鐩稿叧鐨勪紶杈撹澶�*************锛岀浉鍏崇殑浼犺緭璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"浼犺緭涓撲笟鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 08:56:14 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":16.0,\"minute\":56.0,\"second\":14.0,\"nano\":2.0836E8}}}],\"/api/fault/dataAlarmQuery\":[{\"url\":\"http://localhost:9100/api/fault/dataAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"searchType\\\": 1,\\n  \\\"deviceType\\\": \\\"\\\",\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"clearancereportflag\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754297821199\\\",\\\"ticket\\\":null,\\\"respCode\\\":-1,\\\"respMsg\\\":\\\"鎿嶄綔澶辫触\\\"},\\\"body\\\":null,\\\"success\\\":false}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 08:57:01 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":16.0,\"minute\":57.0,\"second\":1.0,\"nano\":2.029723E8}}},{\"url\":\"http://localhost:9100/api/fault/dataAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"searchType\\\": 1,\\n  \\\"deviceType\\\": \\\"\\\",\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"clearancereportflag\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754297932197\\\",\\\"ticket\\\":null,\\\"respCode\\\":-1,\\\"respMsg\\\":\\\"鎿嶄綔澶辫触\\\"},\\\"body\\\":null,\\\"success\\\":false}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 08:58:52 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":8.0,\"day\":4.0},\"time\":{\"hour\":16.0,\"minute\":58.0,\"second\":52.0,\"nano\":2.002563E8}}},{\"url\":\"http://localhost:9100/api/fault/dataAlarmQuery\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n  \\\"deviceName\\\": \\\"\\\",\\n  \\\"deviceIp\\\": \\\"*************\\\",\\n  \\\"startTime\\\": \\\"2025-08-05 10:03:00\\\",\\n  \\\"specialty\\\": \\\"无线(中兴5G)\\\",\\n  \\\"operationType\\\": 1,\\n  \\\"searchType\\\": 1,\\n  \\\"deviceType\\\": \\\"\\\",\\n  \\\"tgName\\\": \\\"图谱名称\\\",\\n  \\\"clearancereportflag\\\": 0\\n}\",\"raw_para\":[{\"key\":\"deviceName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备名称\",\"is_checked\":1,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"deviceIp\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备IP\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"startTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"告警发生时间\",\"is_checked\":1,\"not_null\":\"1\",\"field_type\":\"String\"},{\"key\":\"specialty\",\"value\":\"\",\"type\":\"Text\",\"description\":\"专业\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"operationType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"操作类型：0-传输专业故障，1-具体故障类型\",\"is_checked\":1,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"searchType\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"查询类型：0-图谱查询对端设备，1-查询传输设备告警\",\"is_checked\":1,\"not_null\":\"1\",\"field_type\":\"Integer\"},{\"key\":\"deviceType\",\"value\":\"\",\"type\":\"Text\",\"description\":\"设备类型\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tgName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"图谱名称\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"clearancereportflag\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"head\\\":{\\\"resTime\\\":\\\"1754298037792\\\",\\\"ticket\\\":null,\\\"respCode\\\":0,\\\"respMsg\\\":\\\"operate success\\\"},\\\"body\\\":{\\\"result\\\":\\\"鏍规嵁璧勬簮鎵胯浇鍥捐氨鍒嗘瀽锛氱浉鍏崇殑鏁版嵁璁惧*************锛岀浉鍏崇殑鏁版嵁璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛涙牴鎹祫婧愭壙杞藉浘璋卞垎鏋愶細鐩稿叧鐨勬暟鎹澶�*************锛岀浉鍏崇殑鏁版嵁璁惧*************鍦�2025-07-21 10:35:50鍙戠敓DU灏忓尯閫�鏈嶏紱瀵艰嚧鍙戠敓鏁呴殰锛�\\\",\\\"fultName\\\":\\\"鏁版嵁涓撲笟鏁呴殰\\\"},\\\"success\\\":true}\",\"responseHeader\":{\"Keep-Alive\":[\"timeout\\u003d60\"],\"Transfer-Encoding\":[\"chunked\"],\"Connection\":[\"keep-alive\"],\"Date\":[\"Mon, 04 Aug 2025 09:00:37 GMT\"],\"Content-Type\":[\"application/json\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025,\"month\":8,\"day\":4},\"time\":{\"hour\":17,\"minute\":0,\"second\":38,\"nano\":95354400}}}]}",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "b4ad4240c4194df00a0c2c787eefd370",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="CloudDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="hn-api-nacos" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ffcs.oss.CloudDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="466932b6-c822-4708-b0a5-f2278d358880" name="Changes" comment="" />
      <created>1753861258969</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753861258969</updated>
      <workItem from="1753861261150" duration="2060000" />
      <workItem from="1753964791078" duration="15036000" />
      <workItem from="1754269002404" duration="14000" />
      <workItem from="1754269149897" duration="21000000" />
    </task>
    <task id="LOCAL-00001" summary="【新增】查询局站动环告警">
      <option name="closed" value="true" />
      <created>1754035774328</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754035774328</updated>
    </task>
    <task id="LOCAL-00002" summary="【新增】查询传输、数据设备告警">
      <option name="closed" value="true" />
      <created>1754298281162</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754298281162</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/feature/l4" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="【新增】查询局站动环告警" />
    <MESSAGE value="【新增】查询传输、数据设备告警" />
    <option name="LAST_COMMIT_MESSAGE" value="【新增】查询传输、数据设备告警" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java</url>
          <line>803</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java</url>
          <line>958</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java</url>
          <line>1032</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>