package com.ffcs.oss.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
@EnableOpenApi
public class SwaggerConfig {

    // Swagger实例Bean是Docket，所以通过配置Docket实例来配置Swagger
    @Bean
    public Docket restApi(Environment environment) {

        // 设置要显示swagger的环境
        Profiles of = Profiles.of("dev", "test");//test环境不存在也可以写
        // 判断当前是否处于该环境
        // 通过 enable() 接收此参数判断是否要显示
        boolean b = environment.acceptsProfiles(of);

        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("def")
                .apiInfo(apiInfo("故障根因查询API", "1.0"))
//                .enable(b)  // 如果是false，swagger将不能在浏览器中访问了
                .useDefaultResponseMessages(true)
                .forCodeGeneration(false)
                .select()
                // RequestHandlerSelectors：配置要扫描接口的方式
                .apis(RequestHandlerSelectors.basePackage("com.ffcs.oss.controller"))
                .paths(PathSelectors.any()) // 过滤什么路径
                .build();
    }

    /**
     * 创建该API的基本信息（这些基本信息会展现在文档页面中）
     * 访问地址：http://ip:port/swagger-ui.html
     *
     * @return
     */
    // 可以通过apiInfo()属性配置文档信息
    private ApiInfo apiInfo(String title, String version) {
        return new ApiInfoBuilder()
                .title(title)
                .description("故障根因查询")
                .termsOfServiceUrl("xxx.xxx.xxx")
                .contact(new Contact("故障根因查询", "*********", "*********"))
                .version(version)
                .build();
    }
}
