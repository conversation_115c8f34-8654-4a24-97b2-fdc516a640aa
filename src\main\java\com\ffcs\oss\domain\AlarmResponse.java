package com.ffcs.oss.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 综告系统告警查询响应实体
 */
@Data
public class AlarmResponse {
    private AlarmResponseBody body;
    private AlarmResponseHead head;
    private Boolean success;

    @Data
    public static class AlarmResponseHead {
        private String resTime;
        private String ticket;
        private Integer respCode;
        private String respMsg;
    }

    @Data
    public static class AlarmResponseBody {
        private Integer pageNo;
        private Integer pageSize;
        private Integer total;
        private List<AlarmRecord> records;
    }

    @Data
    public static class AlarmRecord {
        private AlarmSrc alarmSrc;
        private String specialty;
        private AlarmExt alarmExt;
        private String clearancereportflag;
        private String equipmentname;
        private List<RelatedInfo> relatedInfo;
        private String originaleventtime;
        private String firstalarmtime;
        private String ocname;
        private String alarmobjectid;
        private String locationinfo;
        private String additionaltext;
        private String faultid;
        private String alarmtitle;
        private String alarminfo;
        private String problemoccurrences;
        private NeTag neTag;
    }

    @Data
    public static class AlarmSrc {
        @JSONField(name = "SRC_ALARM_ID")
        private String srcAlarmId;
    }

    @Data
    public static class AlarmExt {
        @JSONField(name = "ADDKEYWORD4")
        private String addKeyword4;
        @JSONField(name = "ADDKEYWORD3")
        private String addKeyword3;
    }

    @Data
    public static class RelatedInfo {
        @JSONField(name = "PARENT_ID")
        private String parentId;
    }

    @Data
    public static class NeTag {
        @JSONField(name = "NE_ID")
        private String neId;
    }
}
