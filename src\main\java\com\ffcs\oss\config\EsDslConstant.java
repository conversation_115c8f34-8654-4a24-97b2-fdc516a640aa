package com.ffcs.oss.config;

public class EsDslConstant {


    public static final String QUERY_CELL_SITE_ALARM_DSL = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"siteName\": \"#cellSite#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"bool\": {\n" +
            "            \"should\": [\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*基站直流供电异常告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*BBU电池欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RRU电池欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*电压异常*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*BBU干接点欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RRU干接点欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*基站板卡电压异常告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点2:直流欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点1:直流欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点2:欠压告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点3:直流欠压*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*电源故障*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RRU掉电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*BBU掉电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*射频单元直流掉电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RRU掉电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RRU干接点掉电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*POE供电异常*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点1:交流停电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*单板电源关断*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点2:交流停电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*干接点3:交流停电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*基站直流供电异常告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*主用电源输入欠压告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*交流停电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*基站掉电通知*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*设备掉电*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*射频单元输入电源能力不足告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*射频单元交流掉电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*RHUB交流掉电告警*\" } },\n" +
            "              { \"wildcard\": { \"alarmTitle\": \"*输入电源断*\" } }\n" +
            "            ],\n" +
            "            \"minimum_should_match\": 1\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"range\": {\n" +
            "            \"eventTime\": {\n" +
            "              \"gte\": \"" + "#rusultTime#" + "\",\n" +
            "              \"lte\": \"" + "#startTime#" + "\",\n" +
            "              \"format\": \"yyyy-MM-dd HH:mm:ss\"\n" +
            "            }\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}";

    public static final String POSITIONING_CLOUD_CAUSE_FAULT_DSL = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"range\": {\n" +
            "            \"eventTime\": {\n" +
            "              \"gte\": \"" + "#rusultTime#" + "\",\n" +
            "              \"lte\": \"" + "#startTime#" + "\",\n" +
            "              \"format\": \"yyyy-MM-dd HH:mm:ss\"\n" +
            "            }\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"exists\": {\n" +
            "            \"field\": \"ipaddress\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"terms\": {\n" +
            "            \"alarmTitle\": #alarmTitles#\n" +
            "          }\n" +
            "        }\n" +
            "      ],\n" +
            "      \"must_not\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"ipaddress\": \"\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}";



}
