# 承载故障根因分析功能说明

## 功能概述

承载故障根因分析功能是基于现有的海南API系统开发的新功能，主要用于分析设备故障的根本原因。该功能通过查询图谱能力获取设备所在局站信息，然后查询综告系统获取相关告警数据，最终返回故障根因分析结果。

## 接口信息

### 接口地址
```
POST /api/hn/carrierFaultAnalysis
```

### 请求参数
```json
{
    "deviceName": "设备名称",
    "deviceIp": "设备IP地址", 
    "startTime": "告警发生时间",
    "specialty": "专业",
    "operationType": 0
}
```

#### 参数说明
- `deviceName`: 设备名称（可选，与deviceIp至少填写一个）
- `deviceIp`: 设备IP地址（可选，与deviceName至少填写一个）
- `startTime`: 告警发生时间（必填，格式：yyyy-MM-dd HH:mm:ss）
- `specialty`: 专业名称（可选，用于告警过滤）
- `operationType`: 操作类型（必填，0-动环故障，1-停电）

### 响应结果
```json
{
    "respCode": 0,
    "respMsg": "操作成功",
    "data": {
        "result": "根据资源承载图谱分析：设备名称设备位于局站名称，因局站名称在告警时间发生告警标题；导致设备名称发生故障；",
        "fultName": "停电"
    }
}
```

## 业务逻辑

### 1. 局站查询逻辑
1. **优先使用IP查询**：如果传入了deviceIp，优先调用图谱能力接口根据IP查询局站编码
2. **设备名称查询**：如果IP查询不到结果，则使用deviceName调用图谱能力接口查询局站编码
3. **无局站信息**：如果IP和设备名称都查询不到局站信息，返回错误码2，错误信息"无设备所在局站信息"

### 2. 告警查询逻辑
1. 根据获取到的局站编码，调用综告系统接口查询告警数据
2. 查询条件包括：局站编码、告警状态（默认为1）、告警发生时间
3. 对查询结果按告警ID去重处理

### 3. 告警过滤逻辑
1. 如果nacos配置了告警名称且传入了specialty参数，则根据配置过滤告警
2. 如果nacos未配置或specialty为空，则不进行过滤，返回全量告警
3. 过滤规则：告警标题包含配置的告警名称即匹配

### 4. 结果返回逻辑
根据operationType参数返回不同的分析结果：

#### operationType = 1（停电）
- 返回结果格式：`根据资源承载图谱分析：{设备名称}设备位于{局站名称}，因{局站名称}在{告警时间}发生{告警标题}；导致{设备名称}发生故障；`
- fultName：`停电`

#### operationType = 0（动环故障）
- 返回结果格式：`根据资源承载图谱分析：{设备名称}设备位于{局站名称}，因{局站名称}在{告警时间}发生停电故障；故障识别为动环故障；`
- fultName：`动环故障`

#### 无告警数据
- 返回结果格式：`根据资源承载图谱分析：{设备名称}位于{局站编码}，该设备故障发生时段，{局站编码}运行正常未发生停电故障；下一步排查传输设备`
- fultName：`下一步排查传输设备`

## 配置说明

### 1. application.yml配置
```yaml
hnconfig:
  query-station-by-ip-url: http://localhost:9100/api/test/get4  # 图谱能力查询设备IP对应局站编码的URL
  query-alarm-url: http://localhost:9100/api/test/get5          # 综告系统查询告警的URL
```

### 2. Nacos配置
在nacos中创建配置文件`alarm-config.yml`，配置不同专业对应的告警名称：

```yaml
alarm:
  names:
    "无线(中兴5G)":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
    "传输":
      - "光缆中断"
      - "设备断电"
      - "链路故障"
    "动环":
      - "停电"
      - "欠压"
      - "高温"
```

## 数据库脚本

执行`database-scripts.sql`中的脚本来创建或确保相关表结构的存在。主要包括：
- `kg.cm_device`: 设备信息表
- `rm_area`: 区域信息表
- `kg.device_station_mapping`: 设备与局站映射表（可选）
- `kg.alarm_name_config`: 告警名称配置表（可选）

## 测试说明

### 测试数据
当配置`hnconfig.if-test=Y`时，系统会返回模拟的测试数据，便于功能测试。

### 测试用例
```bash
curl -X POST http://localhost:8080/api/hn/carrierFaultAnalysis \
  -H "Content-Type: application/json" \
  -d '{
    "deviceName": "NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维",
    "deviceIp": "*************",
    "startTime": "2024-09-27 10:35:50",
    "specialty": "无线(中兴5G)",
    "operationType": 1
  }'
```

## 注意事项

1. **参数校验**：deviceName和deviceIp至少需要传入一个
2. **时间格式**：startTime必须为标准时间格式
3. **专业匹配**：specialty参数需要与nacos配置中的专业名称完全匹配
4. **告警过滤**：告警名称支持模糊匹配，只要告警标题包含配置的名称即可
5. **性能考虑**：建议对频繁查询的设备IP和局站映射关系进行缓存
6. **错误处理**：所有外部接口调用都有异常处理，确保系统稳定性
